#!/usr/bin/env bun
import { User } from '../src/models/index.js';
import sequelize from '../src/database/connection.js';

async function createAdminUser() {
  try {
    console.log('🔧 Creating new admin user...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');
    
    // Sync the User model (create table if it doesn't exist)
    await User.sync();
    
    // Admin user details
    const adminData = {
      username: 'admin',
      email: '<EMAIL>',
      full_name: 'System Administrator',
      role: 'admin',
      is_active: true
    };
    
    // Default password (user should change this immediately)
    const defaultPassword = 'Admin123!';
    
    // Check if admin user already exists
    const existingAdmin = await User.findByUsername(adminData.username);
    
    if (existingAdmin) {
      console.log('⚠️  Admin user already exists. Updating password...');
      
      // Hash the new password
      const hashedPassword = await User.hashPassword(defaultPassword);
      
      // Update the existing admin user
      await existingAdmin.update({
        password_hash: hashedPassword,
        failed_login_attempts: 0,
        locked_until: null,
        is_active: true
      });
      
      console.log('✅ Admin user password updated successfully!');
    } else {
      console.log('👤 Creating new admin user...');
      
      // Hash the password
      const hashedPassword = await User.hashPassword(defaultPassword);
      
      // Create the admin user
      const adminUser = await User.create({
        ...adminData,
        password_hash: hashedPassword
      });
      
      console.log('✅ Admin user created successfully!');
    }
    
    console.log('\n📋 Admin Login Details:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`👤 Username: ${adminData.username}`);
    console.log(`📧 Email: ${adminData.email}`);
    console.log(`🔑 Password: ${defaultPassword}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('\n⚠️  IMPORTANT: Please change the password immediately after login!');
    console.log('🌐 Login URL: http://localhost:8080/login');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    
    if (error.name === 'SequelizeValidationError') {
      console.error('Validation errors:');
      error.errors.forEach(err => {
        console.error(`  - ${err.message}`);
      });
    } else if (error.name === 'SequelizeUniqueConstraintError') {
      console.error('User with this username or email already exists');
    }
    
    process.exit(1);
  } finally {
    // Close database connection
    await sequelize.close();
  }
}

// Run the script
if (import.meta.main) {
  createAdminUser();
}

export { createAdminUser };
