services:
  mariadb:
    image: mariadb:latest
    container_name: cmms_mariadb
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 67Bifmm23!
      MYSQL_DATABASE: cmms_db
      MYSQL_USER: cmms_user
      MYSQL_PASSWORD: cmms_password
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    command: --default-authentication-plugin=mysql_native_password
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/migrations:/docker-entrypoint-initdb.d/migrations
      - ./docker/mariadb/init:/docker-entrypoint-initdb.d/init
    networks:
      - cmms_network
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      start_period: 10s
      interval: 10s
      timeout: 5s
      retries: 3

  # Optional: phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: cmms_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mariadb
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: 67Bifmm23!
      MYSQL_ROOT_PASSWORD: 67Bifmm23!
    ports:
      - "8081:80"
    depends_on:
      mariadb:
        condition: service_healthy
    networks:
      - cmms_network

volumes:
  mariadb_data:
    driver: local

networks:
  cmms_network:
    driver: bridge
