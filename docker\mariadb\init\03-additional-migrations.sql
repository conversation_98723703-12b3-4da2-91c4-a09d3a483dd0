-- Additional CMMS Database Migrations
USE cmms_db;

-- Migration 007: Create machine groups
CREATE TABLE IF NOT EXISTS machine_groups (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
  description TEXT,
  department VARCHA<PERSON>(255),
  supervisor <PERSON><PERSON><PERSON><PERSON>(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_name (name),
  INDEX idx_department (department)
);

-- Add group_id to machines table if it doesn't exist
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'machines' 
   AND column_name = 'group_id' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE machines ADD COLUMN group_id INT NULL AFTER department',
  'SELECT "group_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key for group_id if it doesn't exist
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
   WHERE table_name = 'machines' 
   AND column_name = 'group_id' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE machines ADD FOREIGN KEY (group_id) REFERENCES machine_groups(id) ON DELETE SET NULL',
  'SELECT "group foreign key already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Migration: Create parts table (enhanced spare_parts)
CREATE TABLE IF NOT EXISTS parts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  part_number VARCHAR(100) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  manufacturer VARCHAR(255),
  category VARCHAR(100),
  location VARCHAR(255),
  quantity_in_stock INT DEFAULT 0,
  minimum_stock INT DEFAULT 0,
  maximum_stock INT DEFAULT NULL,
  unit_price DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'EUR',
  supplier VARCHAR(255),
  supplier_part_number VARCHAR(100),
  lead_time_days INT DEFAULT 0,
  is_critical BOOLEAN DEFAULT FALSE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_part_number (part_number),
  INDEX idx_name (name),
  INDEX idx_category (category),
  INDEX idx_location (location),
  INDEX idx_critical (is_critical),
  INDEX idx_stock_level (quantity_in_stock, minimum_stock)
);

-- Create part orders table
CREATE TABLE IF NOT EXISTS part_orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_number VARCHAR(100) UNIQUE NOT NULL,
  supplier VARCHAR(255) NOT NULL,
  order_date DATE NOT NULL,
  expected_delivery_date DATE,
  actual_delivery_date DATE,
  status ENUM('pending', 'ordered', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
  total_cost DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'EUR',
  notes TEXT,
  created_by VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_order_number (order_number),
  INDEX idx_supplier (supplier),
  INDEX idx_status (status),
  INDEX idx_order_date (order_date)
);

-- Create part order items table
CREATE TABLE IF NOT EXISTS part_order_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_id INT NOT NULL,
  part_id INT NOT NULL,
  quantity_ordered INT NOT NULL,
  quantity_received INT DEFAULT 0,
  unit_price DECIMAL(10,2),
  total_price DECIMAL(10,2),
  notes TEXT,
  
  FOREIGN KEY (order_id) REFERENCES part_orders(id) ON DELETE CASCADE,
  FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE CASCADE,
  INDEX idx_order_id (order_id),
  INDEX idx_part_id (part_id)
);

-- Create part usage history table
CREATE TABLE IF NOT EXISTS part_usage_history (
  id INT AUTO_INCREMENT PRIMARY KEY,
  part_id INT NOT NULL,
  machine_id INT,
  maintenance_request_id INT,
  issue_id INT,
  quantity_used INT NOT NULL,
  unit_cost DECIMAL(10,2),
  total_cost DECIMAL(10,2),
  used_by VARCHAR(255),
  used_date DATE NOT NULL,
  purpose ENUM('maintenance', 'repair', 'installation', 'replacement', 'other') DEFAULT 'maintenance',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE CASCADE,
  FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE SET NULL,
  FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE SET NULL,
  FOREIGN KEY (issue_id) REFERENCES issues(id) ON DELETE SET NULL,
  INDEX idx_part_id (part_id),
  INDEX idx_machine_id (machine_id),
  INDEX idx_used_date (used_date),
  INDEX idx_purpose (purpose)
);

-- Create maintenance materials table
CREATE TABLE IF NOT EXISTS maintenance_materials (
  id INT AUTO_INCREMENT PRIMARY KEY,
  maintenance_request_id INT NOT NULL,
  part_id INT NOT NULL,
  quantity_planned INT NOT NULL DEFAULT 1,
  quantity_used INT DEFAULT 0,
  unit_cost DECIMAL(10,2),
  total_cost DECIMAL(10,2),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE CASCADE,
  FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE CASCADE,
  INDEX idx_maintenance_request_id (maintenance_request_id),
  INDEX idx_part_id (part_id),
  UNIQUE KEY unique_maintenance_part (maintenance_request_id, part_id)
);

-- Create maintenance materials summary view
CREATE OR REPLACE VIEW maintenance_materials_summary AS
SELECT 
  mr.id as maintenance_request_id,
  mr.title as maintenance_title,
  COUNT(mm.id) as total_materials,
  SUM(mm.quantity_planned) as total_quantity_planned,
  SUM(mm.quantity_used) as total_quantity_used,
  SUM(mm.total_cost) as total_materials_cost
FROM maintenance_requests mr
LEFT JOIN maintenance_materials mm ON mr.id = mm.maintenance_request_id
GROUP BY mr.id, mr.title;

-- Create issue materials table
CREATE TABLE IF NOT EXISTS issue_materials (
  id INT AUTO_INCREMENT PRIMARY KEY,
  issue_id INT NOT NULL,
  part_id INT NOT NULL,
  quantity_used INT NOT NULL DEFAULT 1,
  unit_cost DECIMAL(10,2),
  total_cost DECIMAL(10,2),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (issue_id) REFERENCES issues(id) ON DELETE CASCADE,
  FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE CASCADE,
  INDEX idx_issue_id (issue_id),
  INDEX idx_part_id (part_id),
  UNIQUE KEY unique_issue_part (issue_id, part_id)
);

-- Create issue materials summary view
CREATE OR REPLACE VIEW issue_materials_summary AS
SELECT 
  i.id as issue_id,
  i.title as issue_title,
  COUNT(im.id) as total_materials,
  SUM(im.quantity_used) as total_quantity_used,
  SUM(im.total_cost) as total_materials_cost
FROM issues i
LEFT JOIN issue_materials im ON i.id = im.issue_id
GROUP BY i.id, i.title;
