import { User, Session, LoginAttempt } from '../models/index.js';

// Authentication middleware
const requireAuth = async (c, next) => {
  try {
    const sessionId = c.req.cookie('session_id');
    
    if (!sessionId) {
      return c.redirect('/login');
    }
    
    const session = await Session.findByIdWithUser(sessionId);
    
    if (!session || session.isExpired()) {
      // Clean up expired session
      if (session) {
        await session.destroy();
      }
      
      // Clear cookie
      c.res.headers.set('Set-Cookie', 'session_id=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly');
      return c.redirect('/login');
    }
    
    // Extend session
    await session.extend();
    
    // Add user to context
    c.set('user', session.user);
    c.set('session', session);
    
    await next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return c.redirect('/login');
  }
};

// Role-based authorization middleware
const requireRole = (requiredRole) => {
  return async (c, next) => {
    const user = c.get('user');
    
    if (!user) {
      return c.redirect('/login');
    }
    
    if (!user.hasPermission(requiredRole)) {
      return c.html(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Juurdepääs keelatud</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100">
          <div class="min-h-screen flex items-center justify-center">
            <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
              <div class="text-red-500 text-6xl mb-4">🚫</div>
              <h1 class="text-2xl font-bold text-gray-800 mb-4">Juurdepääs keelatud</h1>
              <p class="text-gray-600 mb-6">Teil puuduvad õigused selle lehe vaatamiseks.</p>
              <p class="text-sm text-gray-500 mb-6">Vajalik roll: <strong>${requiredRole}</strong><br>Teie roll: <strong>${user.role}</strong></p>
              <a href="/admin" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Tagasi avalehele
              </a>
            </div>
          </div>
        </body>
        </html>
      `, 403);
    }
    
    await next();
  };
};

// Optional auth middleware (doesn't redirect if not authenticated)
const optionalAuth = async (c, next) => {
  try {
    const sessionId = c.req.cookie('session_id');
    
    if (sessionId) {
      const session = await Session.findByIdWithUser(sessionId);
      
      if (session && !session.isExpired()) {
        await session.extend();
        c.set('user', session.user);
        c.set('session', session);
      }
    }
    
    await next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    await next();
  }
};

// Rate limiting middleware for login attempts
const loginRateLimit = async (c, next) => {
  try {
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
    
    const isBlocked = await LoginAttempt.isIpBlocked(ipAddress);
    
    if (isBlocked) {
      return c.html(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Liiga palju katseid</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100">
          <div class="min-h-screen flex items-center justify-center">
            <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
              <div class="text-yellow-500 text-6xl mb-4">⚠️</div>
              <h1 class="text-2xl font-bold text-gray-800 mb-4">Liiga palju katseid</h1>
              <p class="text-gray-600 mb-6">Teie IP-aadress on ajutiselt blokeeritud liiga paljude ebaõnnestunud sisselogimiskatsete tõttu.</p>
              <p class="text-sm text-gray-500">Palun oodake 15 minutit enne uuesti proovimist.</p>
            </div>
          </div>
        </body>
        </html>
      `, 429);
    }
    
    await next();
  } catch (error) {
    console.error('Rate limit middleware error:', error);
    await next();
  }
};

// Redirect authenticated users away from login page
const redirectIfAuthenticated = async (c, next) => {
  try {
    const sessionId = c.req.cookie('session_id');
    
    if (sessionId) {
      const session = await Session.findByIdWithUser(sessionId);
      
      if (session && !session.isExpired()) {
        return c.redirect('/admin');
      }
    }
    
    await next();
  } catch (error) {
    console.error('Redirect middleware error:', error);
    await next();
  }
};

export {
  requireAuth,
  requireRole,
  optionalAuth,
  loginRateLimit,
  redirectIfAuthenticated
};
