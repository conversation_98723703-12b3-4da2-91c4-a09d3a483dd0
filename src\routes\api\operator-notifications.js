import { Hono } from 'hono';
import { Notification } from '../../models/Notification.js';
import { Machine } from '../../models/Machine.js';
import { validateQRMiddleware } from '../../middleware/qr-security.js';

export const operatorNotificationApiRoutes = new Hono();

// POST /api/operator-notifications/:machineNumber/:secret - Send notification from operator view
operatorNotificationApiRoutes.post('/:machineNumber/:secret', validateQRMiddleware, async c => {
  try {
    console.log('=== OPERATOR NOTIFICATION REQUEST ===');
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    console.log('Machine Number:', machineNumber);
    console.log('Machine found:', machine ? machine.id : 'NOT FOUND');

    if (!machine) {
      return c.json({ error: 'Machine not found' }, 404);
    }

    const { to_roles, title, message, type } = await c.req.json();
    
    console.log('Request body:', { to_roles, title, message, type });

    if (!title || !message) {
      return c.json({ error: 'Title and message are required' }, 400);
    }

    if (!to_roles || to_roles.length === 0) {
      return c.json({ error: 'Recipients must be specified' }, 400);
    }

    console.log('Sending to roles:', to_roles);

    // Send notifications to specified roles
    const notifications = await Notification.sendToRoles(
      null, // from_user_id is null for operator messages
      to_roles,
      title,
      message,
      type || 'general',
      machine.id
    );

    console.log('Notifications sent:', notifications.length);
    console.log('=== END OPERATOR NOTIFICATION ===');

    return c.json({
      success: true,
      message: 'Notification(s) sent successfully',
      notifications
    });
  } catch (error) {
    console.error('Error sending operator notification:', error);
    return c.json({ error: 'Failed to send notification' }, 500);
  }
});
