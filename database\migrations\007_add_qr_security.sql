-- Add QR security features
-- Migration: 007_add_qr_security.sql

-- Add QR secret column to machines table
ALTER TABLE machines 
ADD COLUMN qr_secret VARCHAR(32) NULL;

-- Create QR access logs table for security monitoring
CREATE TABLE IF NOT EXISTS qr_access_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  machine_id INT NULL,
  machine_number VARCHAR(50) NULL,
  secret_attempt VARCHAR(32) NULL,
  ip_address VARCHAR(45) NOT NULL,
  user_agent TEXT NULL,
  access_type ENUM('valid', 'invalid_secret', 'invalid_machine', 'rate_limited') NOT NULL,
  success BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_machine_id (machine_id),
  INDEX idx_ip_address (ip_address),
  INDEX idx_created_at (created_at),
  INDEX idx_access_type (access_type),
  FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE SET NULL
);

-- Create rate limiting table for QR code access
CREATE TABLE IF NOT EXISTS qr_rate_limits (
  id INT AUTO_INCREMENT PRIMARY KEY,
  ip_address VARCHAR(45) NOT NULL,
  attempts INT NOT NULL DEFAULT 1,
  window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  blocked_until TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_ip (ip_address),
  INDEX idx_ip_address (ip_address),
  INDEX idx_blocked_until (blocked_until)
);
