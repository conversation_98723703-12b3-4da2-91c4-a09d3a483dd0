-- Migration 017: Add operator visibility to documents table
-- Date: 2025-09-01

-- Add operator_visible column to documents table
ALTER TABLE documents 
ADD COLUMN operator_visible BOOLEAN DEFAULT FALSE COMMENT 'Kas dokument on operaatorile nähtav QR vaates';

-- Add index for operator visibility queries
CREATE INDEX idx_documents_operator_visible ON documents(operator_visible);

-- Update existing documents - set safety and manual documents as operator visible by default
UPDATE documents 
SET operator_visible = TRUE 
WHERE document_type IN ('manual', 'safety_guide');

-- Add document category column for better organization
ALTER TABLE documents 
ADD COLUMN category ENUM('safety', 'operation', 'maintenance', 'technical', 'other') DEFAULT 'other' 
COMMENT 'Dokumendi kategooria operaatori vaates';

-- Create index for category
CREATE INDEX idx_documents_category ON documents(category);

-- Update existing documents with appropriate categories
UPDATE documents SET category = 'safety' WHERE document_type = 'safety_guide';
UPDATE documents SET category = 'operation' WHERE document_type IN ('manual', 'operating_procedure');
UPDATE documents SET category = 'technical' WHERE document_type IN ('schematic', 'drawing');
UPDATE documents SET category = 'maintenance' WHERE document_type = 'maintenance_log';
