import { 
  validateQRAccess, 
  logQRAccess, 
  checkRateLimit, 
  isInternalNetwork 
} from '../services/qr-security.js';

/**
 * Middleware to validate QR code access with security checks
 */
export async function validateQRMiddleware(c, next) {
  console.log('🔍 QR Middleware started');
  try {
    const { machineNumber, secret } = c.req.param();
    console.log('📱 Processing QR request for machine:', machineNumber);
    
    // Get client IP address
    const clientIP = c.req.header('X-Forwarded-For') ||
                    c.req.header('X-Real-IP') ||
                    c.req.header('CF-Connecting-IP') ||
                    c.env?.ip ||
                    '127.0.0.1';

    const userAgent = c.req.header('User-Agent') || 'Unknown';

    // Debug logging
    console.log('=== QR ACCESS ATTEMPT ===');
    console.log('Machine Number:', machineNumber);
    console.log('Client IP:', clientIP);
    console.log('User Agent:', userAgent);
    console.log('All Headers:', Object.fromEntries(c.req.raw.headers.entries()));
    console.log('========================');

    // Check if IP is from internal network
    if (!isInternalNetwork(clientIP)) {
      await logQRAccess({
        machineId: null,
        machineNumber,
        secretAttempt: secret,
        ipAddress: clientIP,
        userAgent,
        accessType: 'external_ip',
        success: false
      });

      return c.html(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>403 - Juurdepääs keelatud</title>
          <meta charset="UTF-8">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #dc3545; }
          </style>
        </head>
        <body>
          <h1>403 - Juurdepääs keelatud</h1>
          <div class="alert-error error">
            <p>QR-koodide kasutamine on lubatud ainult ettevõtte sisevõrgust.</p>
            <p>Palun kasutage QR-koodi ettevõtte territooriumil.</p>
          </div>
        </body>
        </html>
      `, 403);
    }

    // Check rate limiting
    const rateLimitResult = await checkRateLimit(clientIP);
    
    if (!rateLimitResult.allowed) {
      await logQRAccess({
        machineId: null,
        machineNumber,
        secretAttempt: secret,
        ipAddress: clientIP,
        userAgent,
        accessType: 'rate_limited',
        success: false
      });

      const resetTime = new Date(rateLimitResult.resetTime).toLocaleTimeString('et-EE');
      
      return c.html(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>429 - Liiga palju päringuid</title>
          <meta charset="UTF-8">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #dc3545; }
          </style>
        </head>
        <body>
          <h1>429 - Liiga palju päringuid</h1>
          <div class="alert-error error">
            <p>Olete ületanud QR-koodide kasutamise limiidi (10 korda 15 minuti jooksul).</p>
            <p>Proovige uuesti pärast kell ${resetTime}.</p>
            <p>Katseid: ${rateLimitResult.attempts}</p>
          </div>
        </body>
        </html>
      `, 429);
    }

    // Validate QR access
    const validation = await validateQRAccess(machineNumber, secret);
    
    if (!validation.valid) {
      // Log failed attempt
      await logQRAccess({
        machineId: null,
        machineNumber,
        secretAttempt: secret,
        ipAddress: clientIP,
        userAgent,
        accessType: validation.error,
        success: false
      });

      return c.html(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>404 - QR-kood ei ole kehtiv</title>
          <meta charset="UTF-8">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #dc3545; }
          </style>
        </head>
        <body>
          <h1>404 - Lehte ei leitud</h1>
          <div class="alert-error error">
            <p>QR-kood ei ole kehtiv või on aegunud.</p>
            <p>Palun kontrollige QR-koodi või võtke ühendust administraatoriga.</p>
          </div>
        </body>
        </html>
      `, 404);
    }

    // Log successful access
    await logQRAccess({
      machineId: validation.machine.id,
      machineNumber: validation.machine.machine_number,
      secretAttempt: secret,
      ipAddress: clientIP,
      userAgent,
      accessType: 'valid',
      success: true
    });

    // Store machine info in context for next middleware
    c.set('machine', validation.machine);
    c.set('clientIP', clientIP);
    
    await next();
  } catch (error) {
    console.error('QR validation middleware error:', error);
    
    return c.html(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>500 - Serveri viga</title>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          .error { color: #dc3545; }
        </style>
      </head>
      <body>
        <h1>500 - Serveri viga</h1>
        <div class="alert-error error">
          <p>Vabandust, tekkis tehniline viga.</p>
          <p>Palun proovige hiljem uuesti või võtke ühendust administraatoriga.</p>
        </div>
      </body>
      </html>
    `, 500);
  }
}

/**
 * Middleware to check admin access for QR management
 */
export function requireAdminForQR(c, next) {
  // This would integrate with your existing auth system
  // For now, we'll assume admin check is done elsewhere
  return next();
}
