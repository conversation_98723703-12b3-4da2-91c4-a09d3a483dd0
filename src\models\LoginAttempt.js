import { pool } from '../config/database.js';

export class LoginAttempt {
  constructor(data) {
    this.id = data.id;
    this.username = data.username;
    this.ip_address = data.ip_address;
    this.user_agent = data.user_agent;
    this.success = data.success;
    this.failure_reason = data.failure_reason;
    this.created_at = data.created_at;
  }

  static async logAttempt(username, ipAddress, userAgent, success, failureReason = null) {
    try {
      await pool.execute(
        `INSERT INTO login_attempts (username, ip_address, user_agent, success, failure_reason) 
         VALUES (?, ?, ?, ?, ?)`,
        [username, ipAddress, userAgent, success ? 1 : 0, failureReason]
      );
    } catch (error) {
      console.error('Error logging login attempt:', error);
    }
  }

  static async getRecentAttempts(ipAddress, minutes = 15) {
    try {
      const [rows] = await pool.execute(
        `SELECT COUNT(*) as count FROM login_attempts 
         WHERE ip_address = ? AND success = 0 AND created_at > DATE_SUB(NOW(), INTERVAL ? MINUTE)`,
        [ipAddress, minutes]
      );
      return rows[0].count;
    } catch (error) {
      console.error('Error getting recent attempts:', error);
      return 0;
    }
  }

  static async isIpBlocked(ipAddress, maxAttempts = 5, minutes = 15) {
    try {
      const recentAttempts = await this.getRecentAttempts(ipAddress, minutes);
      return recentAttempts >= maxAttempts;
    } catch (error) {
      console.error('Error checking if IP is blocked:', error);
      return false;
    }
  }

  static async findAll(limit = 100) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM login_attempts ORDER BY created_at DESC LIMIT ?',
        [limit]
      );
      return rows.map(row => new LoginAttempt(row));
    } catch (error) {
      console.error('Error finding login attempts:', error);
      return [];
    }
  }
}
