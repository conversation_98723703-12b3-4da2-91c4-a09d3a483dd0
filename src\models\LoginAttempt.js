import { DataTypes } from 'sequelize';
import sequelize from '../database/connection.js';

const LoginAttempt = sequelize.define('LoginAttempt', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: false
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  success: {
    type: DataTypes.BOOLEAN,
    allowNull: false
  },
  failure_reason: {
    type: DataTypes.STRING(100),
    allowNull: true
  }
}, {
  tableName: 'login_attempts',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false
});

// Static methods
LoginAttempt.logAttempt = async function(username, ipAddress, userAgent, success, failureReason = null) {
  return await this.create({
    username,
    ip_address: ipAddress,
    user_agent: userAgent,
    success,
    failure_reason: failureReason
  });
};

LoginAttempt.getRecentFailedAttempts = async function(ipAddress, minutes = 15) {
  const since = new Date(Date.now() - minutes * 60 * 1000);
  
  return await this.count({
    where: {
      ip_address: ipAddress,
      success: false,
      created_at: {
        [sequelize.Sequelize.Op.gte]: since
      }
    }
  });
};

LoginAttempt.isIpBlocked = async function(ipAddress, maxAttempts = 10, minutes = 15) {
  const failedAttempts = await this.getRecentFailedAttempts(ipAddress, minutes);
  return failedAttempts >= maxAttempts;
};

LoginAttempt.getLoginHistory = async function(limit = 100, offset = 0) {
  return await this.findAndCountAll({
    order: [['created_at', 'DESC']],
    limit,
    offset
  });
};

LoginAttempt.getSuccessfulLogins = async function(username, limit = 10) {
  return await this.findAll({
    where: {
      username,
      success: true
    },
    order: [['created_at', 'DESC']],
    limit
  });
};

export default LoginAttempt;
