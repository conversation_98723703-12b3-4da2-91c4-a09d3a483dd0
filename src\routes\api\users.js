import { Hono } from 'hono';
import { Op } from 'sequelize';
import { User } from '../../models/index.js';
import { requireAuth, requireRole } from '../../middleware/auth.js';

const userApiRoutes = new Hono();

// Create new user
userApiRoutes.post('/', requireAuth, requireRole('admin'), async c => {
  try {
    const body = await c.req.json();
    const { full_name, username, email, role, password, password_confirm } = body;

    // Validate required fields
    if (!full_name || !username || !email || !role || !password) {
      return c.json({
        success: false,
        message: 'Kõik väljad on kohustuslikud'
      }, 400);
    }

    // Validate passwords match
    if (password !== password_confirm) {
      return c.json({
        success: false,
        message: 'Paroolid ei kattu'
      }, 400);
    }

    // Validate password strength
    const passwordValidation = User.validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return c.json({
        success: false,
        message: passwordValidation.errors.join(', ')
      }, 400);
    }

    // Check if username already exists
    const existingUser = await User.findOne({ where: { username } });
    if (existingUser) {
      return c.json({
        success: false,
        message: 'Kasutajanimi on juba kasutusel'
      }, 400);
    }

    // Check if email already exists
    const existingEmail = await User.findOne({ where: { email } });
    if (existingEmail) {
      return c.json({
        success: false,
        message: 'E-mail on juba kasutusel'
      }, 400);
    }

    // Hash password
    const password_hash = await User.hashPassword(password);

    // Create user
    const user = await User.create({
      full_name,
      username,
      email,
      role,
      password_hash,
      is_active: true
    });

    return c.json({
      success: true,
      message: 'Kasutaja edukalt loodud',
      user: {
        id: user.id,
        full_name: user.full_name,
        username: user.username,
        email: user.email,
        role: user.role,
        is_active: user.is_active
      }
    });

  } catch (error) {
    console.error('Error creating user:', error);
    return c.json({
      success: false,
      message: 'Viga kasutaja loomisel'
    }, 500);
  }
});

// Toggle user status (activate/deactivate)
userApiRoutes.patch('/:id/toggle', requireAuth, requireRole('admin'), async c => {
  try {
    const userId = c.req.param('id');
    const body = await c.req.json();
    const { is_active } = body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return c.json({
        success: false,
        message: 'Kasutajat ei leitud'
      }, 404);
    }

    // Prevent deactivating the main admin user
    if (user.username === 'admin' && !is_active) {
      return c.json({
        success: false,
        message: 'Peamist admin kasutajat ei saa deaktiveerida'
      }, 400);
    }

    // Update user status
    await user.update({ is_active });

    return c.json({
      success: true,
      message: `Kasutaja ${is_active ? 'aktiveeritud' : 'deaktiveeritud'}`,
      user: {
        id: user.id,
        full_name: user.full_name,
        username: user.username,
        is_active: user.is_active
      }
    });

  } catch (error) {
    console.error('Error toggling user status:', error);
    return c.json({
      success: false,
      message: 'Viga kasutaja staatuse muutmisel'
    }, 500);
  }
});

// Get user details
userApiRoutes.get('/:id', requireAuth, requireRole('admin'), async c => {
  try {
    const userId = c.req.param('id');

    const user = await User.findByPk(userId);
    if (!user) {
      return c.json({
        success: false,
        message: 'Kasutajat ei leitud'
      }, 404);
    }

    return c.json({
      success: true,
      user: {
        id: user.id,
        full_name: user.full_name,
        username: user.username,
        email: user.email,
        role: user.role,
        is_active: user.is_active,
        last_login_at: user.last_login_at,
        created_at: user.created_at
      }
    });

  } catch (error) {
    console.error('Error getting user:', error);
    return c.json({
      success: false,
      message: 'Viga kasutaja andmete laadimisel'
    }, 500);
  }
});

// Update user
userApiRoutes.patch('/:id', requireAuth, requireRole('admin'), async c => {
  try {
    const userId = c.req.param('id');
    const body = await c.req.json();
    const { full_name, email, role } = body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return c.json({
        success: false,
        message: 'Kasutajat ei leitud'
      }, 404);
    }

    // Prevent changing main admin user's role
    if (user.username === 'admin' && role !== 'admin') {
      return c.json({
        success: false,
        message: 'Peamist admin kasutaja rolli ei saa muuta'
      }, 400);
    }

    // Check if email is already in use by another user
    if (email && email !== user.email) {
      const existingEmail = await User.findOne({
        where: {
          email,
          id: { [Op.ne]: userId }
        }
      });
      if (existingEmail) {
        return c.json({
          success: false,
          message: 'E-mail on juba kasutusel'
        }, 400);
      }
    }

    // Update user
    const updateData = {};
    if (full_name) updateData.full_name = full_name;
    if (email) updateData.email = email;
    if (role) updateData.role = role;

    await user.update(updateData);

    return c.json({
      success: true,
      message: 'Kasutaja andmed uuendatud',
      user: {
        id: user.id,
        full_name: user.full_name,
        username: user.username,
        email: user.email,
        role: user.role,
        is_active: user.is_active
      }
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return c.json({
      success: false,
      message: 'Viga kasutaja andmete uuendamisel'
    }, 500);
  }
});

// Get all users (for admin)
userApiRoutes.get('/', requireAuth, requireRole('admin'), async c => {
  try {
    const users = await User.findAll({
      order: [['created_at', 'DESC']],
      attributes: { exclude: ['password_hash'] }
    });

    return c.json({
      success: true,
      users
    });

  } catch (error) {
    console.error('Error getting users:', error);
    return c.json({
      success: false,
      message: 'Viga kasutajate laadimisel'
    }, 500);
  }
});

export { userApiRoutes };
