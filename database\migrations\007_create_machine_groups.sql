-- Create machine groups table
CREATE TABLE machine_groups (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
  description TEXT,
  color VARCHAR(7) DEFAULT '#3B82F6', -- Hex color for visual identification
  icon VARCHAR(50) DEFAULT 'fas fa-cogs', -- Font Awesome icon class
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Add group_id to machines table
ALTER TABLE machines 
ADD COLUMN group_id INT NULL AFTER location,
ADD FOREIGN KEY fk_machine_group (group_id) REFERENCES machine_groups(id) ON DELETE SET NULL;

-- Create index for group_id
CREATE INDEX idx_machine_group_id ON machines(group_id);

-- Insert some default machine groups
INSERT INTO machine_groups (name, description, color, icon) VALUES
('Toot<PERSON>liinid', 'Põhilised tootmisliinid ja konveierid', '#10B981', 'fas fa-industry'),
('CNC Masinad', 'Arvutijuhitavad töötlusmasinad', '#3B82F6', 'fas fa-microchip'),
('Pakkimisseadmed', 'Toodete pakkimise ja märgistamise seadmed', '#F59E0B', 'fas fa-box'),
('Kvaliteedikontroll', 'Testimise ja kvaliteedikontrolli seadmed', '#8B5CF6', 'fas fa-search'),
('Logistika', 'Ladustamise ja transpordi seadmed', '#EF4444', 'fas fa-truck'),
('Abiseadmed', 'Toetavad ja abistavad seadmed', '#6B7280', 'fas fa-tools');

-- Update some existing machines to have groups for testing
UPDATE machines 
SET group_id = (SELECT id FROM machine_groups WHERE name = 'Tootmisliinid' LIMIT 1)
WHERE machine_number LIKE 'PROD-%' OR name LIKE '%tootmis%' OR name LIKE '%liin%';

UPDATE machines 
SET group_id = (SELECT id FROM machine_groups WHERE name = 'CNC Masinad' LIMIT 1)
WHERE machine_number LIKE 'CNC-%' OR name LIKE '%CNC%' OR name LIKE '%freesim%';

UPDATE machines 
SET group_id = (SELECT id FROM machine_groups WHERE name = 'Pakkimisseadmed' LIMIT 1)
WHERE machine_number LIKE 'PACK-%' OR name LIKE '%pakkim%';

UPDATE machines 
SET group_id = (SELECT id FROM machine_groups WHERE name = 'Kvaliteedikontroll' LIMIT 1)
WHERE machine_number LIKE 'QC-%' OR name LIKE '%kontroll%' OR name LIKE '%test%';

UPDATE machines 
SET group_id = (SELECT id FROM machine_groups WHERE name = 'Logistika' LIMIT 1)
WHERE machine_number LIKE 'LOG-%' OR name LIKE '%transport%' OR name LIKE '%ladu%';

-- Set remaining machines to Abiseadmed
UPDATE machines 
SET group_id = (SELECT id FROM machine_groups WHERE name = 'Abiseadmed' LIMIT 1)
WHERE group_id IS NULL;
