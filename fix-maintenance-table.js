import { pool } from './src/config/database.js';
import { readFileSync } from 'fs';

async function createMaintenanceRequestsTable() {
  try {
    console.log('Creating maintenance_requests table...');
    
    // Read the migration file
    const migrationSQL = readFileSync('./database/migrations/016_create_maintenance_requests_table.sql', 'utf8');
    
    // Execute the SQL
    await pool.execute(migrationSQL);
    
    console.log('✅ maintenance_requests table created successfully');
    
    // Verify the table structure
    const [rows] = await pool.execute('DESCRIBE maintenance_requests');
    console.log('Table structure:', rows);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    process.exit();
  }
}

createMaintenanceRequestsTable();
