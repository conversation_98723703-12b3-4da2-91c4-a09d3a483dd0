import { pool } from '../config/database.js';

export class MaintenanceRequest {
  // Check for potential duplicate maintenance requests
  static async checkForDuplicates(requestData) {
    try {
      const { machine_id, operator_number, title, description } = requestData;

      // Look for similar maintenance requests in the last 30 minutes
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

      const [rows] = await pool.execute(
        `SELECT id, title, description, created_at
         FROM maintenance_requests
         WHERE machine_id = ?
           AND operator_number = ?
           AND title = ?
           AND created_at > ?
           AND status IN ('requested', 'scheduled', 'in_progress')
         ORDER BY created_at DESC
         LIMIT 1`,
        [machine_id, operator_number, title, thirtyMinutesAgo]
      );

      if (rows.length > 0) {
        const existingRequest = rows[0];
        // If description is also very similar, it's likely a duplicate
        if (description && existingRequest.description) {
          const similarity = MaintenanceRequest.calculateSimilarity(description, existingRequest.description);
          if (similarity > 0.8) { // 80% similarity threshold
            return {
              isDuplicate: true,
              existingRequest: existingRequest,
              message: `Sarnane hoolduse taotlus on juba registreeritud ${Math.round((Date.now() - new Date(existingRequest.created_at)) / 60000)} minutit tagasi (ID: #${existingRequest.id})`
            };
          }
        } else if (!description && !existingRequest.description) {
          // Both have no description, likely duplicate
          return {
            isDuplicate: true,
            existingRequest: existingRequest,
            message: `Sama hoolduse taotlus on juba registreeritud ${Math.round((Date.now() - new Date(existingRequest.created_at)) / 60000)} minutit tagasi (ID: #${existingRequest.id})`
          };
        }
      }

      return { isDuplicate: false };
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      // Don't block creation if duplicate check fails
      return { isDuplicate: false };
    }
  }

  // Simple text similarity calculation
  static calculateSimilarity(text1, text2) {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);

    const allWords = new Set([...words1, ...words2]);
    const intersection = words1.filter(word => words2.includes(word));

    return intersection.length / allWords.size;
  }

  static async create(requestData) {
    try {
      const {
        machine_id,
        operator_number,
        operator_name = null,
        maintenance_type = 'preventive',
        urgency = 'medium',
        title,
        description = null,
        requested_date = null,
        photo_filename = null,
        photo_data = null,
        photo_mime_type = null,
        photo_size = null,
      } = requestData;

      const [result] = await pool.execute(
        `INSERT INTO maintenance_requests (
          machine_id, operator_number, operator_name,
          maintenance_type, urgency, title, description, requested_date,
          photo_filename, photo_data, photo_mime_type, photo_size
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          machine_id,
          operator_number,
          operator_name,
          maintenance_type,
          urgency,
          title,
          description,
          requested_date,
          photo_filename,
          photo_data,
          photo_mime_type,
          photo_size,
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to create maintenance request: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          mr.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location,
          mp.company_name as partner_company,
          mp.contact_person as partner_contact,
          mp.email as partner_email,
          mp.phone as partner_phone
         FROM maintenance_requests mr
         JOIN machines m ON mr.machine_id = m.id
         LEFT JOIN maintenance_partners mp ON mr.partner_id = mp.id
         WHERE mr.id = ?`,
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find maintenance request: ${error.message}`);
    }
  }

  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT
          mr.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location,
          mp.company_name as partner_company,
          mp.contact_person as partner_contact,
          mp.email as partner_email,
          mp.phone as partner_phone
        FROM maintenance_requests mr
        JOIN machines m ON mr.machine_id = m.id
        LEFT JOIN maintenance_partners mp ON mr.partner_id = mp.id
      `;

      const conditions = [];
      const params = [];

      if (filters.machine_id) {
        conditions.push('mr.machine_id = ?');
        params.push(filters.machine_id);
      }

      if (filters.status) {
        conditions.push('mr.status = ?');
        params.push(filters.status);
      }

      if (filters.urgency) {
        conditions.push('mr.urgency = ?');
        params.push(filters.urgency);
      }

      if (filters.maintenance_type) {
        conditions.push('mr.maintenance_type = ?');
        params.push(filters.maintenance_type);
      }

      if (filters.scheduled_date_from) {
        conditions.push('mr.scheduled_date >= ?');
        params.push(filters.scheduled_date_from);
      }

      if (filters.scheduled_date_to) {
        conditions.push('mr.scheduled_date <= ?');
        params.push(filters.scheduled_date_to);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' ORDER BY mr.created_at DESC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to fetch maintenance requests: ${error.message}`);
    }
  }

  static async findByMachineId(machineId) {
    try {
      return await this.findAll({ machine_id: machineId });
    } catch (error) {
      throw new Error(`Failed to find maintenance requests for machine: ${error.message}`);
    }
  }

  static async update(id, updateData) {
    try {
      const fields = [];
      const params = [];

      // Build dynamic update query
      for (const [key, value] of Object.entries(updateData)) {
        if (value !== undefined) {
          fields.push(`${key} = ?`);
          params.push(value);
        }
      }

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      params.push(id);

      await pool.execute(
        `UPDATE maintenance_requests SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        params
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update maintenance request: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      const [result] = await pool.execute('DELETE FROM maintenance_requests WHERE id = ?', [id]);

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete maintenance request: ${error.message}`);
    }
  }

  static async getStatistics() {
    try {
      const [rows] = await pool.execute(`
        SELECT
          COUNT(*) as total_requests,
          SUM(CASE WHEN status = 'requested' THEN 1 ELSE 0 END) as pending_requests,
          SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled_requests,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_requests,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_requests,
          SUM(CASE WHEN urgency = 'urgent' THEN 1 ELSE 0 END) as urgent_requests,
          SUM(CASE WHEN urgency = 'high' THEN 1 ELSE 0 END) as high_urgency_requests,
          SUM(CASE WHEN maintenance_type = 'emergency' THEN 1 ELSE 0 END) as emergency_requests
        FROM maintenance_requests
      `);

      return rows[0];
    } catch (error) {
      throw new Error(`Failed to get maintenance request statistics: ${error.message}`);
    }
  }

  static async getUpcomingMaintenance(days = 30) {
    try {
      const [rows] = await pool.execute(
        `
        SELECT mr.*, m.machine_number, m.name as machine_name, m.location as machine_location
        FROM maintenance_requests mr
        JOIN machines m ON mr.machine_id = m.id
        WHERE mr.scheduled_date IS NOT NULL
        AND mr.scheduled_date <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
        AND mr.status IN ('scheduled', 'in_progress')
        ORDER BY mr.scheduled_date ASC
      `,
        [days]
      );

      return rows;
    } catch (error) {
      throw new Error(`Failed to get upcoming maintenance: ${error.message}`);
    }
  }

  static async getOverdueMaintenance() {
    try {
      const [rows] = await pool.execute(`
        SELECT mr.*, m.machine_number, m.name as machine_name, m.location as machine_location
        FROM maintenance_requests mr
        JOIN machines m ON mr.machine_id = m.id
        WHERE mr.scheduled_date IS NOT NULL
        AND mr.scheduled_date < CURDATE()
        AND mr.status IN ('scheduled', 'in_progress')
        ORDER BY mr.scheduled_date ASC
      `);

      return rows;
    } catch (error) {
      throw new Error(`Failed to get overdue maintenance: ${error.message}`);
    }
  }

  // Calendar-specific methods
  static async getCalendarEvents(startDate, endDate) {
    try {
      const [rows] = await pool.execute(`
        SELECT 
          mr.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location
        FROM maintenance_requests mr
        JOIN machines m ON mr.machine_id = m.id
        WHERE (
          (mr.scheduled_date IS NOT NULL AND mr.scheduled_date BETWEEN ? AND ?)
          OR (mr.scheduled_date IS NULL AND mr.requested_date IS NOT NULL AND mr.requested_date BETWEEN ? AND ?)
        )
        AND mr.status IN ('scheduled', 'in_progress', 'requested')
        ORDER BY COALESCE(mr.scheduled_date, mr.requested_date) ASC, mr.urgency DESC
      `, [startDate, endDate, startDate, endDate]);

      return rows.map(request => ({
        id: request.id,
        title: request.title,
        // Fields used by calendar UI
        start: request.scheduled_date || request.requested_date,
        end: request.scheduled_date || request.requested_date, // No due_date in current table
        // Original field names expected by calendar grid & JS
        scheduled_date: request.scheduled_date || request.requested_date,
        due_date: request.scheduled_date || request.requested_date, // Fallback since no due_date column
        description: request.description,
        notes: request.maintenance_notes,
        machine_number: request.machine_number,
        machine_name: request.machine_name,
        machine: {
          id: request.machine_id,
          number: request.machine_number,
          name: request.machine_name,
          location: request.machine_location
        },
        maintenance_type: request.maintenance_type,
        priority: request.urgency, // Map urgency to priority for UI consistency
        status: request.status,
        assigned_to: request.assigned_to,
        estimated_duration_hours: request.estimated_duration ? (request.estimated_duration / 60) : null, // Convert from minutes
        estimated_cost: request.cost,
        color: this.getEventColor(request.urgency, request.status, request.maintenance_type)
      }));
    } catch (error) {
      throw new Error(`Failed to get calendar events: ${error.message}`);
    }
  }

  static getEventColor(priority, status, type) {
    // Status-based colors
    if (status === 'in_progress') return '#f59e0b'; // amber
    if (status === 'completed') return '#10b981'; // green
    if (status === 'cancelled') return '#6b7280'; // gray

    // Priority-based colors for scheduled maintenance
    switch (priority) {
      case 'critical': return '#ef4444'; // red
      case 'high': return '#f97316'; // orange
      case 'medium': return '#3b82f6'; // blue
      case 'low': return '#8b5cf6'; // purple
      default: return '#6b7280'; // gray
    }
  }

  static async getCalendarStats(startDate, endDate) {
    try {
      const [rows] = await pool.execute(`
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
          SUM(CASE WHEN urgency = 'urgent' THEN 1 ELSE 0 END) as critical,
          SUM(CASE WHEN scheduled_date < CURDATE() AND status IN ('scheduled', 'requested') THEN 1 ELSE 0 END) as overdue,
          SUM(cost) as total_estimated_cost,
          SUM(CASE WHEN estimated_duration IS NOT NULL THEN estimated_duration / 60.0 ELSE 0 END) as total_estimated_hours
        FROM maintenance_requests
        WHERE scheduled_date BETWEEN ? AND ?
      `, [startDate, endDate]);

      return rows[0] || {
        total: 0,
        scheduled: 0,
        in_progress: 0,
        completed: 0,
        critical: 0,
        overdue: 0,
        total_estimated_cost: 0,
        total_estimated_hours: 0
      };
    } catch (error) {
      throw new Error(`Failed to get calendar stats: ${error.message}`);
    }
  }

  static async scheduleMaintenanceFromCalendar(data) {
    try {
      const {
        machine_id,
        title,
        description,
        maintenance_type = 'preventive',
        priority = 'medium', // Map to urgency
        scheduled_date,
        due_date, // This field doesn't exist in current table, ignore
        estimated_duration_hours,
        assigned_to,
        estimated_cost,
        notes, // Map to maintenance_notes
        requested_by
      } = data;

      // Map priority to urgency (the actual column name)
      const urgency = priority;
      
      // Map estimated_duration_hours to estimated_duration (convert to minutes if needed)
      const estimated_duration = estimated_duration_hours ? Math.round(parseFloat(estimated_duration_hours) * 60) : null;
      
      // Map estimated_cost to cost
      const cost = estimated_cost;
      
      // Map notes to maintenance_notes
      const maintenance_notes = notes;

      const [result] = await pool.execute(
        `INSERT INTO maintenance_requests (
          machine_id, title, description, maintenance_type, urgency,
          scheduled_date, estimated_duration, assigned_to,
          cost, maintenance_notes, status, requested_date, operator_number
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'scheduled', CURDATE(), ?)`,
        [
          machine_id, title, description, maintenance_type, urgency,
          scheduled_date, estimated_duration, assigned_to,
          cost, maintenance_notes, requested_by || 'admin'
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to schedule maintenance: ${error.message}`);
    }
  }

  static async updateSchedule(id, scheduleData) {
    try {
      const allowedFields = [
        'scheduled_date', 'due_date', 'estimated_duration_hours', 
        'assigned_to', 'estimated_cost', 'notes', 'status'
      ];
      
      const fields = [];
      const values = [];

      for (const [key, value] of Object.entries(scheduleData)) {
        if (allowedFields.includes(key) && value !== undefined) {
          fields.push(`${key} = ?`);
          values.push(value);
        }
      }

      if (fields.length === 0) {
        throw new Error('No valid fields to update');
      }

      values.push(id);

      const [result] = await pool.execute(
        `UPDATE maintenance_requests SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );

      if (result.affectedRows === 0) {
        throw new Error('Maintenance request not found');
      }

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update schedule: ${error.message}`);
    }
  }
}
