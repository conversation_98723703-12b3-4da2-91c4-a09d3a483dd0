-- CMMS Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS cmms_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE cmms_db;

-- Create user (optional)
-- CREATE USER 'cmms_user'@'localhost' IDENTIFIED BY 'cmms_password';
-- GRANT ALL PRIVILEGES ON cmms_db.* TO 'cmms_user'@'localhost';
-- FLUSH PRIVILEGES;

-- machines (masinad)
CREATE TABLE machines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_number VARCHAR(50) UNIQUE NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    manufacturer VARCHA<PERSON>(255),
    model VARCHAR(255),
    serial_number VARCHAR(255),
    manufacturing_year INT,
    department VARCHAR(255),
    responsible_operator VARCHAR(255),
    location VARCHAR(255),
    status ENUM('online', 'offline', 'maintenance') DEFAULT 'online',
    last_maintenance DATE,
    next_maintenance DATE,
    warranty_end DATE,
    qr_code_data LONGBLOB,
    qr_code_mime_type VARCHAR(100) DEFAULT 'image/png',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- operators (operaatorid)
CREATE TABLE operators (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operator_number VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    department VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- issues (rikked)
CREATE TABLE issues (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    operator_number VARCHAR(50) NOT NULL,
    issue_type ENUM('mechanical', 'electrical', 'hydraulic', 'software', 'other') NOT NULL,
    issue_category ENUM('issue', 'maintenance') NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    photo_filename VARCHAR(255),
    photo_data LONGBLOB,
    photo_mime_type VARCHAR(100),
    status ENUM('new', 'in_progress', 'waiting', 'completed', 'cancelled') DEFAULT 'new',
    assigned_to VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);

-- maintenance_records (hoolduskirjed)
CREATE TABLE maintenance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    maintenance_type ENUM('regular', 'emergency', 'preventive') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    performed_by VARCHAR(255),
    scheduled_date DATE,
    completed_date DATE,
    duration_hours DECIMAL(5,2),
    cost DECIMAL(10,2),
    parts_used JSON,
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);

-- spare_parts (varuosad)
CREATE TABLE spare_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    part_number VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    manufacturer VARCHAR(255),
    category VARCHAR(100),
    location VARCHAR(255),
    quantity_in_stock INT DEFAULT 0,
    minimum_stock INT DEFAULT 0,
    unit_price DECIMAL(10,2),
    supplier VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- machine_parts (masina varuosad)
CREATE TABLE machine_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    part_id INT NOT NULL,
    quantity_needed INT DEFAULT 1,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
    FOREIGN KEY (part_id) REFERENCES spare_parts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_machine_part (machine_id, part_id)
);

-- documents (dokumendid)
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    document_type ENUM('manual', 'certificate', 'drawing', 'warranty', 'other') NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_data LONGBLOB NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);

-- development_projects (arendusprojektid)
CREATE TABLE development_projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    initiator VARCHAR(255),
    start_date DATE,
    end_date DATE,
    status ENUM('active', 'completed', 'cancelled', 'on_hold') DEFAULT 'active',
    progress_percentage INT DEFAULT 0,
    tags JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- project_machines (projekti masinad)
CREATE TABLE project_machines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    machine_id INT NOT NULL,
    FOREIGN KEY (project_id) REFERENCES development_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_machine (project_id, machine_id)
);
