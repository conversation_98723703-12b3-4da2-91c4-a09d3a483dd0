-- Create notifications table for in-app messaging
CREATE TABLE IF NOT EXISTS notifications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  from_user_id INT,
  to_user_id INT,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  type <PERSON>NU<PERSON>('info', 'urgent', 'maintenance', 'general') DEFAULT 'general',
  is_read BOOLEAN DEFAULT FALSE,
  machine_id INT DEFAULT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  read_at TIMESTAMP NULL DEFAULT NULL,
  
  INDEX idx_to_user_id (to_user_id),
  INDEX idx_from_user_id (from_user_id),
  INDEX idx_is_read (is_read),
  INDEX idx_created_at (created_at),
  INDEX idx_machine_id (machine_id),
  
  FOREIG<PERSON> KEY (from_user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (to_user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE SET NULL
);
