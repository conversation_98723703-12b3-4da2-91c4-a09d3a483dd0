# 🚀 CMMS Hetzner VPS Deployment Guide (Alpine Linux)

Complete guide for deploying CMMS on Hetzner Cloud VPS using Alpine Linux.

## 🏔️ Why Alpine Linux?

Alpine Linux is perfect for production deployments:

- **🔒 Security-focused:** Minimal attack surface, built-in security features
- **📦 Lightweight:** ~50MB base system vs ~200MB Ubuntu
- **⚡ Fast:** Quick boot times and low resource usage
- **🛡️ Hardened:** Uses musl libc and BusyBox for better security
- **🔄 Simple:** Easy package management with apk

## 💰 Cost Breakdown

| Component | Cost | Notes |
|-----------|------|-------|
| Hetzner CX21 VPS | €4.15/month | 2 vCPU, 4GB RAM, 40GB SSD |
| Domain name | €10-15/year | Optional but recommended |
| SSL Certificate | Free | Let's Encrypt |
| **Total** | **~€5-6/month** | |

## 🏗️ Server Specifications

### Recommended: CX21
- **CPU:** 2 vCPU
- **RAM:** 4GB (Alpine uses ~50% less RAM than Ubuntu)
- **Storage:** 40GB SSD
- **Traffic:** 20TB
- **Price:** €4.15/month

Perfect for CMMS with up to 30 machines and moderate usage.

### For Heavy Usage: CX31
- **CPU:** 2 vCPU
- **RAM:** 8GB
- **Storage:** 80GB SSD
- **Price:** €7.35/month

## 🚀 Quick Deployment (Automated)

### Option 1: One-Line Deployment

```bash
# On your fresh Alpine Linux server
curl -fsSL https://raw.githubusercontent.com/JoonasMagi/CMMS/main/scripts/deploy-hetzner-alpine.sh | sh
```

### Option 2: Manual Step-by-Step

Follow the detailed guide below for full control.

## 📋 Step-by-Step Manual Deployment

### 1. Create Hetzner VPS

1. **Go to** [Hetzner Cloud Console](https://console.hetzner.cloud/)
2. **Create new project** (e.g., "CMMS-Production")
3. **Add server:**
   - **Image:** Alpine Linux 3.19
   - **Type:** CX21
   - **Location:** Choose closest to you
   - **SSH Key:** Add your public SSH key
   - **Name:** cmms-server

### 2. Initial Server Setup

```bash
# Connect to your server
ssh root@YOUR_SERVER_IP

# Update system
apk update && apk upgrade

# Install essential packages
apk add curl wget git nginx certbot mariadb mariadb-client openrc nodejs npm bash openssl

# Setup services
rc-service mariadb setup
rc-service mariadb start
rc-update add mariadb

rc-service nginx start
rc-update add nginx
```

### 3. Install Bun Runtime

```bash
# Install Bun
curl -fsSL https://bun.sh/install | bash

# Add to PATH
echo 'export PATH="$HOME/.bun/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Verify installation
bun --version
```

### 4. Install Process Manager

```bash
# Install PM2 (Node.js already installed)
npm install -g pm2
```

### 5. Setup Database

```bash
# Secure MariaDB
mysql_secure_installation

# Create database and user
mysql -u root -p
```

```sql
CREATE DATABASE cmms_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'cmms_user'@'localhost' IDENTIFIED BY 'your_secure_password_here';
GRANT ALL PRIVILEGES ON cmms_db.* TO 'cmms_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 6. Deploy Application

```bash
# Create application directory
mkdir -p /var/www/cmms
cd /var/www/cmms

# Clone repository
git clone https://github.com/JoonasMagi/CMMS.git .

# Install dependencies
bun install

# Create production environment
cp .env.example .env
nano .env
```

### 7. Configure Environment

Edit `/var/www/cmms/.env`:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=cmms_db
DB_USER=cmms_user
DB_PASSWORD=your_secure_password_here

# Server Configuration
PORT=8080
NODE_ENV=production

# Application URL
BASE_URL=https://yourdomain.com

# Security (generate random 32+ character strings)
SESSION_SECRET=your_very_long_random_session_secret_here
JWT_SECRET=your_very_long_random_jwt_secret_here

# File Management
MAX_FILE_SIZE=10485760

# QR Code Settings
QR_CODE_SIZE=300

# Notifications
NOTIFICATIONS_ENABLED=true
ADMIN_EMAIL=<EMAIL>
```

### 8. Run Database Migrations

```bash
cd /var/www/cmms
bun run migrate
```

### 9. Setup Process Manager

Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'cmms',
    script: 'server.js',
    interpreter: '/root/.bun/bin/bun',
    cwd: '/var/www/cmms',
    env: {
      NODE_ENV: 'production',
      PORT: 8080
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G'
  }]
};
```

```bash
# Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 10. Configure Nginx

Create `/etc/nginx/http.d/cmms.conf`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # File upload support
        client_max_body_size 50M;
    }

    # Static files caching
    location /public/ {
        proxy_pass http://localhost:8080;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# Test and restart
nginx -t
rc-service nginx restart
```

### 11. Setup SSL Certificate

```bash
# Get SSL certificate
certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test auto-renewal
certbot renew --dry-run
```

### 12. Setup Domain DNS

Point your domain to your Hetzner server IP:

```
A Record: yourdomain.com → YOUR_SERVER_IP
A Record: www.yourdomain.com → YOUR_SERVER_IP
```

## 🔧 Maintenance & Monitoring

### Application Management

```bash
# Check status
pm2 status

# View logs
pm2 logs cmms

# Restart application
pm2 restart cmms

# Update application
cd /var/www/cmms
git pull
bun install
pm2 restart cmms
```

### System Monitoring

```bash
# Check system resources
top  # or install htop: apk add htop

# Check disk usage
df -h

# Check memory usage
free -h

# Check Nginx status
rc-service nginx status

# Check MariaDB status
rc-service mariadb status

# Check all services
rc-status
```

### Backup Setup

Create `/root/backup-cmms.sh`:

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/root/backups"
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u cmms_user -p'your_password' cmms_db > $BACKUP_DIR/cmms_db_$DATE.sql

# Keep only last 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

echo "Backup completed: $DATE"
```

```bash
# Make executable
chmod +x /root/backup-cmms.sh

# Add to crontab (daily at 2 AM)
echo '0 2 * * * /root/backup-cmms.sh' | crontab -

# Or edit manually
crontab -e
```

## 🎯 Testing Deployment

1. **Visit your domain:** `https://yourdomain.com`
2. **Login with:** admin / admin123
3. **Test QR codes** with your phone
4. **Check logs:** `pm2 logs cmms`
5. **Test backup:** `/root/backup-cmms.sh`

## 🆘 Troubleshooting

### Application Won't Start

```bash
# Check PM2 logs
pm2 logs cmms

# Check environment file
cat /var/www/cmms/.env

# Test database connection
mysql -u cmms_user -p cmms_db
```

### SSL Issues

```bash
# Check certificate status
certbot certificates

# Renew certificate
certbot renew

# Check Nginx configuration
nginx -t
```

### Performance Issues

```bash
# Check system resources
top  # Alpine's lightweight alternative
df -h
free -h

# Check application logs
pm2 logs cmms

# Restart services
pm2 restart cmms
rc-service nginx restart
```

## 📞 Support

- **GitHub Issues:** [Create an issue](https://github.com/JoonasMagi/CMMS/issues)
- **Documentation:** Check `/docs` folder
- **Hetzner Support:** [Hetzner Help Center](https://docs.hetzner.com/)

## 🎉 Success!

Your CMMS application is now running on Hetzner Cloud with:

- ✅ Secure HTTPS connection
- ✅ Automatic SSL renewal
- ✅ Process monitoring with PM2
- ✅ Automated backups
- ✅ Production-ready configuration

**Default admin credentials:** admin / admin123

**Remember to change the default password after first login!**

## 🏔️ Alpine Linux Advantages for CMMS

### Security Benefits
- **Minimal attack surface:** Only essential packages installed
- **Hardened by default:** Uses musl libc instead of glibc
- **Regular security updates:** Fast security patch deployment
- **No systemd:** Simpler init system (OpenRC) with fewer vulnerabilities

### Performance Benefits
- **Low memory usage:** ~50MB base system vs ~200MB Ubuntu
- **Fast boot times:** Typically 2-3 seconds vs 10-15 seconds
- **Efficient package manager:** apk is faster than apt
- **Small container size:** Perfect for containerization if needed

### Operational Benefits
- **Simple configuration:** Clear, minimal config files
- **Predictable behavior:** Less complexity means fewer surprises
- **Long-term stability:** Conservative package updates
- **Easy maintenance:** Fewer moving parts to manage

### Resource Comparison

| Metric | Alpine Linux | Ubuntu 22.04 | Savings |
|--------|-------------|--------------|---------|
| Base RAM usage | ~50MB | ~200MB | 75% less |
| Base disk usage | ~130MB | ~2.5GB | 95% less |
| Boot time | 2-3 seconds | 10-15 seconds | 70% faster |
| Package count | ~15 base | ~200+ base | 90% fewer |

This makes Alpine perfect for:
- **Cost optimization:** Lower resource usage = lower hosting costs
- **Security compliance:** Minimal attack surface for enterprise environments
- **Performance:** More resources available for your CMMS application
- **Maintenance:** Fewer updates and security patches to manage
