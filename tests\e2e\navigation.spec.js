import { test, expect } from '@playwright/test';

test.describe('Navigation and Role-based Access', () => {
  test('should display correct navigation for admin role', async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');
    await expect(page).toHaveURL('/admin');

    // Check admin navigation items
    await expect(page.locator('nav a[href="/admin"]')).toContainText('Avaleht');
    await expect(page.locator('nav a[href="/machines"]')).toContainText('Masinad');
    await expect(page.locator('nav a[href="/projects"]')).toContainText('Projektid');
    await expect(page.locator('nav a[href="/admin/issues"]')).toContainText('Rikked');
    await expect(page.locator('nav a[href="/admin/maintenance"]')).toContainText('Hooldus');
    await expect(page.locator('nav a[href="/admin/parts"]')).toContainText('Varuosad');
    await expect(page.locator('nav a[href="/admin/reports"]')).toContainText('Aruanded');

    // Admin should see admin dropdown
    await expect(page.locator('[data-testid="admin-dropdown"]')).toBeVisible();
  });

  test('should display correct navigation for maintenance role', async ({ page }) => {
    // Login as maintenance
    await page.goto('/login');
    await page.fill('[name="username"]', 'jaan.tamm');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');
    await expect(page).toHaveURL('/maintenance');

    // Check maintenance navigation items
    await expect(page.locator('nav a[href="/maintenance"]')).toContainText('Avaleht');
    await expect(page.locator('nav a[href="/machines"]')).toContainText('Masinad');
    await expect(page.locator('nav a[href="/projects"]')).toContainText('Projektid');
    await expect(page.locator('nav a[href="/admin/issues"]')).toContainText('Rikked');
    await expect(page.locator('nav a[href="/admin/maintenance"]')).toContainText('Hooldus');
    await expect(page.locator('nav a[href="/admin/parts"]')).toContainText('Varuosad');
    await expect(page.locator('nav a[href="/admin/reports"]')).toContainText('Aruanded');

    // Maintenance should not see admin dropdown
    await expect(page.locator('[data-testid="admin-dropdown"]')).not.toBeVisible();
  });

  test('should display correct navigation for operator role', async ({ page }) => {
    // Login as operator
    await page.goto('/login');
    await page.fill('[name="username"]', 'mari.kask');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');
    await expect(page).toHaveURL('/operator');

    // Check operator navigation items
    await expect(page.locator('nav a[href="/operator"]')).toContainText('Avaleht');
    await expect(page.locator('nav a[href="/machines"]')).toContainText('Masinad');
    await expect(page.locator('nav a[href="/projects"]')).toContainText('Projektid');
    await expect(page.locator('nav a[href="/projects/new"]')).toContainText('Uus projekt');

    // Operator should not see admin functions
    await expect(page.locator('nav a[href="/admin/issues"]')).not.toBeVisible();
    await expect(page.locator('nav a[href="/admin/maintenance"]')).not.toBeVisible();
    await expect(page.locator('nav a[href="/admin/parts"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="admin-dropdown"]')).not.toBeVisible();
  });

  test('should display correct navigation for viewer role', async ({ page }) => {
    // Login as viewer
    await page.goto('/login');
    await page.fill('[name="username"]', 'peeter.saar');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');
    await expect(page).toHaveURL('/viewer');

    // Check viewer navigation items
    await expect(page.locator('nav a[href="/viewer"]')).toContainText('Avaleht');
    await expect(page.locator('nav a[href="/machines"]')).toContainText('Masinad');
    await expect(page.locator('nav a[href="/projects"]')).toContainText('Projektid');
    await expect(page.locator('nav a[href="/admin/reports"]')).toContainText('Aruanded');

    // Viewer should not see admin functions or project creation
    await expect(page.locator('nav a[href="/projects/new"]')).not.toBeVisible();
    await expect(page.locator('nav a[href="/admin/issues"]')).not.toBeVisible();
    await expect(page.locator('nav a[href="/admin/maintenance"]')).not.toBeVisible();
    await expect(page.locator('nav a[href="/admin/parts"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="admin-dropdown"]')).not.toBeVisible();
  });

  test('should highlight active navigation item', async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    // Go to machines page
    await page.goto('/machines');
    await expect(page.locator('nav a[href="/machines"]')).toHaveClass(/bg-blue-800/);

    // Go to projects page
    await page.goto('/projects');
    await expect(page.locator('nav a[href="/projects"]')).toHaveClass(/bg-blue-800/);

    // Go to reports page
    await page.goto('/admin/reports');
    await expect(page.locator('nav a[href="/admin/reports"]')).toHaveClass(/bg-blue-800/);
  });

  test('should handle mobile navigation', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    // Mobile menu should be hidden initially
    await expect(page.locator('[data-testid="mobile-menu"]')).not.toBeVisible();

    // Click mobile menu button
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();

    // Check that navigation items are visible in mobile menu
    await expect(page.locator('[data-testid="mobile-menu"] a[href="/machines"]')).toBeVisible();
    await expect(page.locator('[data-testid="mobile-menu"] a[href="/projects"]')).toBeVisible();

    // Click outside to close mobile menu
    await page.click('body');
    await expect(page.locator('[data-testid="mobile-menu"]')).not.toBeVisible();
  });

  test('should redirect unauthorized users to login', async ({ page }) => {
    // Try to access admin page without login
    await page.goto('/admin');
    await expect(page).toHaveURL('/login');

    // Try to access reports page without login
    await page.goto('/admin/reports');
    await expect(page).toHaveURL('/login');

    // Try to access machines page without login
    await page.goto('/machines');
    await expect(page).toHaveURL('/login');
  });

  test('should prevent role escalation', async ({ page }) => {
    // Login as viewer
    await page.goto('/login');
    await page.fill('[name="username"]', 'peeter.saar');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    // Try to access admin-only pages
    await page.goto('/admin/users');
    await expect(page).toHaveURL('/login'); // Should redirect due to insufficient permissions

    await page.goto('/admin/machine-groups');
    await expect(page).toHaveURL('/login'); // Should redirect due to insufficient permissions

    // Try to access operator-only functions
    await page.goto('/projects/new');
    await expect(page).toHaveURL('/login'); // Should redirect due to insufficient permissions
  });

  test('should maintain session across page navigation', async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    // Navigate through different pages
    await page.goto('/machines');
    await expect(page.locator('nav a[href="/machines"]')).toHaveClass(/bg-blue-800/);

    await page.goto('/projects');
    await expect(page.locator('nav a[href="/projects"]')).toHaveClass(/bg-blue-800/);

    await page.goto('/admin/reports');
    await expect(page.locator('nav a[href="/admin/reports"]')).toHaveClass(/bg-blue-800/);

    // User should remain logged in
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });

  test('should handle logout correctly', async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    // Logout
    await page.goto('/auth/logout');
    await expect(page).toHaveURL('/login');

    // Try to access protected page after logout
    await page.goto('/admin');
    await expect(page).toHaveURL('/login');
  });
});
