import { pool } from '../config/database.js';

export class Issue {
  static async create(issueData) {
    try {
      const {
        machine_id,
        operator_number,
        operator_name = null,
        issue_type = 'other',
        severity = 'medium',
        title,
        description = null,
        photo_filename = null,
        photo_data = null,
        photo_mime_type = null,
        photo_size = null,
      } = issueData;

      const [result] = await pool.execute(
        `INSERT INTO issues (
          machine_id, operator_number, operator_name,
          issue_type, severity, title, description,
          photo_filename, photo_data, photo_mime_type, photo_size
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          machine_id,
          operator_number,
          operator_name,
          issue_type,
          severity,
          title,
          description,
          photo_filename,
          photo_data,
          photo_mime_type,
          photo_size,
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to create issue: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          i.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location,
          mp.company_name as partner_company,
          mp.contact_person as partner_contact,
          mp.email as partner_email,
          mp.phone as partner_phone
         FROM issues i
         JOIN machines m ON i.machine_id = m.id
         LEFT JOIN maintenance_partners mp ON i.partner_id = mp.id
         WHERE i.id = ?`,
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find issue: ${error.message}`);
    }
  }

  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT
          i.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location,
          mp.company_name as partner_company,
          mp.contact_person as partner_contact,
          mp.email as partner_email,
          mp.phone as partner_phone
        FROM issues i
        JOIN machines m ON i.machine_id = m.id
        LEFT JOIN maintenance_partners mp ON i.partner_id = mp.id
      `;

      const conditions = [];
      const params = [];

      if (filters.machine_id) {
        conditions.push('i.machine_id = ?');
        params.push(filters.machine_id);
      }

      if (filters.status) {
        conditions.push('i.status = ?');
        params.push(filters.status);
      }

      if (filters.severity) {
        conditions.push('i.severity = ?');
        params.push(filters.severity);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' ORDER BY i.created_at DESC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to fetch issues: ${error.message}`);
    }
  }

  static async findByMachineId(machineId) {
    try {
      return await this.findAll({ machine_id: machineId });
    } catch (error) {
      throw new Error(`Failed to find issues for machine: ${error.message}`);
    }
  }

  static async update(id, updateData) {
    try {
      const fields = [];
      const params = [];

      // Build dynamic update query
      for (const [key, value] of Object.entries(updateData)) {
        if (value !== undefined) {
          fields.push(`${key} = ?`);
          params.push(value);
        }
      }

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      params.push(id);

      await pool.execute(
        `UPDATE issues SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        params
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update issue: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      const [result] = await pool.execute('DELETE FROM issues WHERE id = ?', [id]);

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete issue: ${error.message}`);
    }
  }

  static async getStatistics() {
    try {
      const [rows] = await pool.execute(`
        SELECT
          COUNT(*) as total_issues,
          SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_issues,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_issues,
          SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_issues,
          SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as critical_issues,
          SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as high_priority_issues
        FROM issues
      `);

      return rows[0];
    } catch (error) {
      throw new Error(`Failed to get issue statistics: ${error.message}`);
    }
  }
}
