import { pool } from '../config/database.js';

export class Issue {
  // Check for potential duplicate issues
  static async checkForDuplicates(issueData) {
    try {
      const { machine_id, operator_number, title, description } = issueData;

      // Look for similar issues in the last 30 minutes
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

      const [rows] = await pool.execute(
        `SELECT id, title, description, created_at
         FROM issues
         WHERE machine_id = ?
           AND operator_number = ?
           AND title = ?
           AND created_at > ?
           AND status IN ('open', 'in_progress')
         ORDER BY created_at DESC
         LIMIT 1`,
        [machine_id, operator_number, title, thirtyMinutesAgo]
      );

      if (rows.length > 0) {
        const existingIssue = rows[0];
        // If description is also very similar, it's likely a duplicate
        if (description && existingIssue.description) {
          const similarity = Issue.calculateSimilarity(description, existingIssue.description);
          if (similarity > 0.8) { // 80% similarity threshold
            return {
              isDuplicate: true,
              existingIssue: existingIssue,
              message: `<PERSON><PERSON><PERSON> rike on juba registreeritud ${Math.round((Date.now() - new Date(existingIssue.created_at)) / 60000)} minutit tagasi (ID: #${existingIssue.id})`
            };
          }
        } else if (!description && !existingIssue.description) {
          // Both have no description, likely duplicate
          return {
            isDuplicate: true,
            existingIssue: existingIssue,
            message: `Sama rike on juba registreeritud ${Math.round((Date.now() - new Date(existingIssue.created_at)) / 60000)} minutit tagasi (ID: #${existingIssue.id})`
          };
        }
      }

      return { isDuplicate: false };
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      // Don't block creation if duplicate check fails
      return { isDuplicate: false };
    }
  }

  // Simple text similarity calculation
  static calculateSimilarity(text1, text2) {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);

    const allWords = new Set([...words1, ...words2]);
    const intersection = words1.filter(word => words2.includes(word));

    return intersection.length / allWords.size;
  }

  static async create(issueData) {
    try {
      const {
        machine_id,
        operator_number,
        operator_name = null,
        issue_type = 'other',
        issue_category = 'issue',
        priority = 'medium',
        severity = 'medium',
        title,
        description = null,
        photo_filename = null,
        photo_data = null,
        photo_mime_type = null,
        photo_size = null,
        status = 'open',
        assigned_to = null,
      } = issueData;

      const [result] = await pool.execute(
        `INSERT INTO issues (
          machine_id, operator_number, operator_name,
          issue_type, issue_category, priority, severity, title, description,
          photo_filename, photo_data, photo_mime_type, photo_size,
          status, assigned_to
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          machine_id,
          operator_number,
          operator_name,
          issue_type,
          issue_category,
          priority,
          severity,
          title,
          description,
          photo_filename,
          photo_data,
          photo_mime_type,
          photo_size,
          status,
          assigned_to,
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to create issue: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          i.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location
         FROM issues i
         JOIN machines m ON i.machine_id = m.id
         WHERE i.id = ?`,
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find issue: ${error.message}`);
    }
  }

  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT
          i.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location
        FROM issues i
        JOIN machines m ON i.machine_id = m.id
      `;

      const conditions = [];
      const params = [];

      if (filters.machine_id) {
        conditions.push('i.machine_id = ?');
        params.push(filters.machine_id);
      }

      if (filters.status) {
        conditions.push('i.status = ?');
        params.push(filters.status);
      }

      if (filters.priority) {
        conditions.push('i.priority = ?');
        params.push(filters.priority);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' ORDER BY i.created_at DESC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to fetch issues: ${error.message}`);
    }
  }

  static async findByMachineId(machineId) {
    try {
      return await this.findAll({ machine_id: machineId });
    } catch (error) {
      throw new Error(`Failed to find issues for machine: ${error.message}`);
    }
  }

  static async update(id, updateData) {
    try {
      const fields = [];
      const params = [];

      // Build dynamic update query
      for (const [key, value] of Object.entries(updateData)) {
        if (value !== undefined) {
          fields.push(`${key} = ?`);
          params.push(value);
        }
      }

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      params.push(id);

      await pool.execute(
        `UPDATE issues SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        params
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update issue: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      const [result] = await pool.execute('DELETE FROM issues WHERE id = ?', [id]);

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete issue: ${error.message}`);
    }
  }

  static async getStatistics() {
    try {
      const [rows] = await pool.execute(`
        SELECT
          COUNT(*) as total_issues,
          SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_issues,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_issues,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_issues,
          SUM(CASE WHEN priority = 'critical' THEN 1 ELSE 0 END) as critical_issues,
          SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority_issues
        FROM issues
      `);

      return rows[0];
    } catch (error) {
      throw new Error(`Failed to get issue statistics: ${error.message}`);
    }
  }

  // Calendar-specific methods for displaying issues in calendar
  static async getCalendarEvents(startDate, endDate) {
    try {
      const [rows] = await pool.execute(`
        SELECT 
          i.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location
        FROM issues i
        JOIN machines m ON i.machine_id = m.id
        WHERE i.created_at BETWEEN ? AND ?
        AND i.status IN ('open', 'in_progress', 'resolved')
        ORDER BY i.created_at DESC, i.priority DESC
      `, [startDate + ' 00:00:00', endDate + ' 23:59:59']);

      return rows.map(issue => ({
        id: issue.id,
        title: `Rike: ${issue.title}`,
        type: 'issue',
        start: issue.created_at.toISOString().split('T')[0],
        end: issue.created_at.toISOString().split('T')[0],
        description: issue.description,
        machine_number: issue.machine_number,
        machine_name: issue.machine_name,
        machine: {
          id: issue.machine_id,
          number: issue.machine_number,
          name: issue.machine_name,
          location: issue.machine_location
        },
        priority: issue.priority,
        severity: issue.severity,
        status: issue.status,
        operator_number: issue.operator_number,
        operator_name: issue.operator_name,
        assigned_to: issue.assigned_to,
        created_at: issue.created_at,
        color: this.getEventColor(issue.priority, issue.status, issue.severity)
      }));
    } catch (error) {
      throw new Error(`Failed to get issue calendar events: ${error.message}`);
    }
  }

  static getEventColor(priority, status, severity) {
    // Status-based colors for issues
    if (status === 'resolved') return '#10b981'; // green
    if (status === 'in_progress') return '#f59e0b'; // amber
    
    // Priority-based colors for open issues
    switch (priority) {
      case 'critical': return '#dc2626'; // red
      case 'high': return '#ea580c'; // orange
      case 'medium': return '#2563eb'; // blue
      case 'low': return '#7c3aed'; // purple
      default: return '#6b7280'; // gray
    }
  }

  static async getCalendarStats(startDate, endDate) {
    try {
      const [rows] = await pool.execute(`
        SELECT
          COUNT(*) as total,
          SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
          SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved,
          SUM(CASE WHEN priority = 'critical' THEN 1 ELSE 0 END) as critical,
          SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority
        FROM issues
        WHERE created_at BETWEEN ? AND ?
      `, [startDate + ' 00:00:00', endDate + ' 23:59:59']);

      return rows[0] || {
        total: 0, open: 0, in_progress: 0, resolved: 0, critical: 0, high_priority: 0
      };
    } catch (error) {
      throw new Error(`Failed to get issue calendar stats: ${error.message}`);
    }
  }
}
