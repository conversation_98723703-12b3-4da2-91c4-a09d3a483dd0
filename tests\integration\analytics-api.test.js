import { describe, it, expect, beforeAll, afterAll } from 'vitest';

describe('Analytics API Integration Tests', () => {
  const baseUrl = 'http://localhost:8080';
  
  beforeAll(async () => {
    // Wait for server to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  describe('GET /api/analytics/advanced', () => {
    it('should return comprehensive analytics data', async () => {
      const response = await fetch(`${baseUrl}/api/analytics/advanced`);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('application/json');
      
      const data = await response.json();
      
      expect(data).toHaveProperty('success', true);
      expect(data).toHaveProperty('costAnalysis');
      expect(data).toHaveProperty('predictiveAnalytics');
      
      // Cost analysis structure
      expect(data.costAnalysis).toHaveProperty('totalMaintenanceCosts');
      expect(data.costAnalysis).toHaveProperty('totalIssueCosts');
      expect(data.costAnalysis).toHaveProperty('costPerMachine');
      expect(data.costAnalysis).toHaveProperty('maintenanceCostsByMonth');
      expect(data.costAnalysis).toHaveProperty('issueCostsByMonth');
      expect(data.costAnalysis).toHaveProperty('roi');
      
      // Predictive analytics structure
      expect(typeof data.predictiveAnalytics).toBe('object');
    });
  });

  describe('GET /api/analytics/cost-analysis', () => {
    it('should return detailed cost analysis', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-01-31';
      const url = `${baseUrl}/api/analytics/cost-analysis?startDate=${startDate}&endDate=${endDate}`;
      
      const response = await fetch(url);
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      
      expect(data).toHaveProperty('success', true);
      expect(data.data).toHaveProperty('totalCosts');
      expect(data.data).toHaveProperty('costBreakdown');
      expect(data.data).toHaveProperty('costTrends');
      expect(data.data).toHaveProperty('costPerMachine');
      expect(data.data).toHaveProperty('roi');
      
      // Verify cost breakdown structure
      expect(data.data.costBreakdown).toHaveProperty('maintenancePercentage');
      expect(data.data.costBreakdown).toHaveProperty('issuePercentage');
      
      // Verify cost trends is an array
      expect(Array.isArray(data.data.costTrends)).toBe(true);
    });
  });

  describe('GET /api/analytics/predictive', () => {
    it('should return predictive analytics data', async () => {
      const response = await fetch(`${baseUrl}/api/analytics/predictive`);
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      
      expect(data).toHaveProperty('success', true);
      expect(data.data).toHaveProperty('machines');
      expect(typeof data.data.machines).toBe('object');
      
      // Check structure of machine data (if any machines exist)
      const machines = Object.values(data.data.machines);
      if (machines.length > 0) {
        const machine = machines[0];
        expect(machine).toHaveProperty('machine_name');
        expect(machine).toHaveProperty('machine_number');
        expect(machine).toHaveProperty('reliabilityScore');
        expect(machine).toHaveProperty('maintenanceRecommendation');
        
        // Verify reliability score is a number between 0-100
        expect(typeof machine.reliabilityScore).toBe('number');
        expect(machine.reliabilityScore).toBeGreaterThanOrEqual(0);
        expect(machine.reliabilityScore).toBeLessThanOrEqual(100);
        
        // Verify maintenance recommendation structure
        expect(machine.maintenanceRecommendation).toHaveProperty('priority');
        expect(machine.maintenanceRecommendation).toHaveProperty('action');
        expect(machine.maintenanceRecommendation).toHaveProperty('color');
      }
    });
  });

  describe('GET /api/analytics/export', () => {
    it('should export cost analysis data in JSON format', async () => {
      const url = `${baseUrl}/api/analytics/export?format=json&type=cost-analysis&startDate=2024-01-01&endDate=2024-01-31`;
      
      const response = await fetch(url);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('application/json');
      expect(response.headers.get('content-disposition')).toContain('attachment');
      
      const data = await response.json();
      
      // Should return cost analysis structure
      expect(data).toHaveProperty('totalMaintenanceCosts');
      expect(data).toHaveProperty('totalIssueCosts');
      expect(data).toHaveProperty('costPerMachine');
    });

    it('should export predictive analytics data in JSON format', async () => {
      const url = `${baseUrl}/api/analytics/export?format=json&type=predictive`;
      
      const response = await fetch(url);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('application/json');
      
      const data = await response.json();
      
      // Should return predictive analytics structure
      expect(typeof data).toBe('object');
    });

    it('should export data in CSV format', async () => {
      const url = `${baseUrl}/api/analytics/export?format=excel&type=cost-analysis&startDate=2024-01-01&endDate=2024-01-31`;
      
      const response = await fetch(url);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-disposition')).toContain('attachment');
      expect(response.headers.get('content-disposition')).toContain('.csv');
      
      const csvData = await response.text();
      
      // Should contain CSV headers
      expect(csvData).toContain('Machine,Maintenance Cost,Issue Cost,Total Cost,ROI %');
    });

    it('should return 400 for missing parameters', async () => {
      const url = `${baseUrl}/api/analytics/export`;
      
      const response = await fetch(url);
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data).toHaveProperty('error');
    });

    it('should return 400 for invalid export type', async () => {
      const url = `${baseUrl}/api/analytics/export?format=json&type=invalid`;
      
      const response = await fetch(url);
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data.error).toContain('Invalid export type');
    });

    it('should return 400 for unsupported format', async () => {
      const url = `${baseUrl}/api/analytics/export?format=xml&type=cost-analysis`;
      
      const response = await fetch(url);
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', false);
      expect(data.error).toContain('Unsupported export format');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid date ranges gracefully', async () => {
      const url = `${baseUrl}/api/analytics/cost-analysis?startDate=invalid&endDate=2024-01-31`;
      
      const response = await fetch(url);
      
      // Should still return 200 but with default date range
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
    });

    it('should handle missing date parameters', async () => {
      const response = await fetch(`${baseUrl}/api/analytics/cost-analysis`);
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('success', true);
      // Should use default date range (last 6 months)
    });
  });

  describe('Data Validation', () => {
    it('should return valid cost data types', async () => {
      const response = await fetch(`${baseUrl}/api/analytics/advanced`);
      const data = await response.json();
      
      expect(typeof data.costAnalysis.totalMaintenanceCosts).toBe('number');
      expect(typeof data.costAnalysis.totalIssueCosts).toBe('number');
      expect(data.costAnalysis.totalMaintenanceCosts).toBeGreaterThanOrEqual(0);
      expect(data.costAnalysis.totalIssueCosts).toBeGreaterThanOrEqual(0);
    });

    it('should return valid predictive analytics data types', async () => {
      const response = await fetch(`${baseUrl}/api/analytics/predictive`);
      const data = await response.json();
      
      Object.values(data.data.machines).forEach(machine => {
        if (machine.mtbf !== null) {
          expect(typeof machine.mtbf).toBe('number');
          expect(machine.mtbf).toBeGreaterThan(0);
        }
        
        if (machine.mttr !== null) {
          expect(typeof machine.mttr).toBe('number');
          expect(machine.mttr).toBeGreaterThan(0);
        }
        
        expect(typeof machine.reliabilityScore).toBe('number');
        expect(machine.reliabilityScore).toBeGreaterThanOrEqual(0);
        expect(machine.reliabilityScore).toBeLessThanOrEqual(100);
      });
    });
  });
});
