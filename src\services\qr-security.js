import { pool } from '../config/database.js';
import crypto from 'crypto';

/**
 * Generate a secure 32-character machine-specific secret
 * @returns {string} 32-character alphanumeric secret
 */
export function generateMachineSecret() {
  return crypto.randomBytes(16).toString('hex');
}

/**
 * Validate QR code access with machine number and secret
 * @param {string} machineNumber - Machine number from URL
 * @param {string} secret - Secret from URL
 * @returns {Object} Validation result with machine data or error
 */
export async function validateQRAccess(machineNumber, secret) {
  try {
    // Find machine by machine number
    const [machines] = await pool.execute(
      'SELECT id, machine_number, name, qr_secret FROM machines WHERE machine_number = ? AND status != "deleted"',
      [machineNumber]
    );

    if (machines.length === 0) {
      return {
        valid: false,
        error: 'machine_not_found'
      };
    }

    const machine = machines[0];

    // Check if machine has a secret
    if (!machine.qr_secret) {
      return {
        valid: false,
        error: 'no_secret'
      };
    }

    // Validate secret
    if (machine.qr_secret !== secret) {
      return {
        valid: false,
        error: 'invalid_secret'
      };
    }

    return {
      valid: true,
      machine: machine
    };
  } catch (error) {
    console.error('Error validating QR access:', error);
    return {
      valid: false,
      error: 'database_error'
    };
  }
}

/**
 * Log QR code access attempt for security monitoring
 * @param {Object} logData - Access attempt data
 */
export async function logQRAccess(logData) {
  try {
    const {
      machineId,
      machineNumber,
      secretAttempt,
      ipAddress,
      userAgent,
      accessType,
      success
    } = logData;

    await pool.execute(
      `INSERT INTO qr_access_logs 
       (machine_id, machine_number, secret_attempt, ip_address, user_agent, access_type, success) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        machineId || null,
        machineNumber || null,
        secretAttempt || null,
        ipAddress,
        userAgent || null,
        accessType,
        success ? 1 : 0
      ]
    );
  } catch (error) {
    console.error('Error logging QR access:', error);
  }
}

/**
 * Check and enforce rate limiting for QR code access
 * @param {string} ipAddress - Client IP address
 * @returns {Object} Rate limit result
 */
export async function checkRateLimit(ipAddress) {
  try {
    const now = new Date();
    const windowStart = new Date(now.getTime() - 15 * 60 * 1000); // 15 minutes ago

    // Get or create rate limit record
    const [existing] = await pool.execute(
      'SELECT * FROM qr_rate_limits WHERE ip_address = ?',
      [ipAddress]
    );

    if (existing.length === 0) {
      // First access - create new record
      await pool.execute(
        'INSERT INTO qr_rate_limits (ip_address, attempts, window_start) VALUES (?, ?, ?)',
        [ipAddress, 1, now]
      );
      
      return {
        allowed: true,
        attempts: 1,
        resetTime: new Date(now.getTime() + 15 * 60 * 1000)
      };
    }

    const record = existing[0];
    const recordWindowStart = new Date(record.window_start);

    // Check if we're in a new time window
    if (recordWindowStart < windowStart) {
      // Reset the window
      await pool.execute(
        'UPDATE qr_rate_limits SET attempts = 1, window_start = ?, blocked_until = NULL WHERE ip_address = ?',
        [now, ipAddress]
      );
      
      return {
        allowed: true,
        attempts: 1,
        resetTime: new Date(now.getTime() + 15 * 60 * 1000)
      };
    }

    // Check if currently blocked
    if (record.blocked_until && new Date(record.blocked_until) > now) {
      return {
        allowed: false,
        attempts: record.attempts,
        resetTime: new Date(record.blocked_until),
        blocked: true
      };
    }

    // Increment attempts
    const newAttempts = record.attempts + 1;
    
    if (newAttempts > 10) {
      // Block for 15 minutes
      const blockUntil = new Date(now.getTime() + 15 * 60 * 1000);
      
      await pool.execute(
        'UPDATE qr_rate_limits SET attempts = ?, blocked_until = ? WHERE ip_address = ?',
        [newAttempts, blockUntil, ipAddress]
      );
      
      return {
        allowed: false,
        attempts: newAttempts,
        resetTime: blockUntil,
        blocked: true
      };
    }

    // Update attempts
    await pool.execute(
      'UPDATE qr_rate_limits SET attempts = ? WHERE ip_address = ?',
      [newAttempts, ipAddress]
    );

    return {
      allowed: true,
      attempts: newAttempts,
      resetTime: new Date(recordWindowStart.getTime() + 15 * 60 * 1000)
    };
  } catch (error) {
    console.error('Error checking rate limit:', error);
    // On error, allow access but log it
    return {
      allowed: true,
      attempts: 1,
      error: true
    };
  }
}

/**
 * Check if IP address is from internal network
 * @param {string} ipAddress - IP address to check
 * @returns {boolean} True if internal network
 */
export function isInternalNetwork(ipAddress) {
  console.log('Checking IP address:', ipAddress, 'NODE_ENV:', process.env.NODE_ENV);

  // In development, allow all IPs
  if (process.env.NODE_ENV === 'development') {
    console.log('Development mode - allowing all IPs');
    return true;
  }

  // Localhost addresses
  if (ipAddress === '127.0.0.1' ||
      ipAddress === '::1' ||
      ipAddress === 'localhost') {
    console.log('Localhost IP - allowed');
    return true;
  }

  // Private network ranges (RFC 1918)
  const privateRanges = [
    /^10\./,                    // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
    /^192\.168\./               // ***********/16
  ];

  const isPrivate = privateRanges.some(range => range.test(ipAddress));
  console.log('Private network check result:', isPrivate);

  // For now, allow all private networks and some common mobile network ranges
  // This is a temporary solution for testing
  if (isPrivate) {
    return true;
  }

  // Allow common mobile/WiFi network ranges for testing
  // This should be configured properly in production
  const mobileRanges = [
    /^192\.168\./, // WiFi networks
    /^10\./,       // Corporate networks
    /^172\./       // Extended private range
  ];

  const isMobile = mobileRanges.some(range => range.test(ipAddress));
  console.log('Mobile network check result:', isMobile);

  return isMobile;
}

/**
 * Generate QR secret for a machine
 * @param {number} machineId - Machine ID
 * @returns {string} Generated secret
 */
export async function generateQRSecretForMachine(machineId) {
  try {
    const secret = generateMachineSecret();
    
    await pool.execute(
      'UPDATE machines SET qr_secret = ? WHERE id = ?',
      [secret, machineId]
    );
    
    return secret;
  } catch (error) {
    console.error('Error generating QR secret for machine:', error);
    throw new Error('Failed to generate QR secret');
  }
}

/**
 * Get QR access logs with pagination
 * @param {Object} options - Query options
 * @returns {Object} Logs and pagination info
 */
export async function getQRAccessLogs(options = {}) {
  try {
    const {
      page = 1,
      limit = 50,
      machineId = null,
      accessType = null,
      startDate = null,
      endDate = null
    } = options;

    const offset = (page - 1) * limit;
    let whereConditions = [];
    let params = [];

    if (machineId) {
      whereConditions.push('machine_id = ?');
      params.push(machineId);
    }

    if (accessType) {
      whereConditions.push('access_type = ?');
      params.push(accessType);
    }

    if (startDate) {
      whereConditions.push('created_at >= ?');
      params.push(startDate);
    }

    if (endDate) {
      whereConditions.push('created_at <= ?');
      params.push(endDate);
    }

    const whereClause = whereConditions.length > 0 
      ? 'WHERE ' + whereConditions.join(' AND ')
      : '';

    // Get logs
    const [logs] = await pool.execute(
      `SELECT * FROM qr_access_logs 
       ${whereClause}
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    // Get total count
    const [countResult] = await pool.execute(
      `SELECT COUNT(*) as total FROM qr_access_logs ${whereClause}`,
      params
    );

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      logs,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error getting QR access logs:', error);
    throw new Error('Failed to get QR access logs');
  }
}
