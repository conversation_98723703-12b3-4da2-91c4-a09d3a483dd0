import { pool } from '../config/database.js';

export class IssueMaterial {
  static async create(materialData) {
    try {
      const {
        issue_id,
        material_type,
        part_id = null,
        material_name = null,
        material_description = null,
        supplier = null,
        quantity = 1,
        unit_of_measure = 'tk',
        unit_cost = 0,
        status = 'planned',
        notes = null,
      } = materialData;

      // Validate required fields based on material type
      if (material_type === 'stock_part' && !part_id) {
        throw new Error('part_id is required for stock_part type');
      }
      if (material_type === 'external_material' && !material_name) {
        throw new Error('material_name is required for external_material type');
      }

      const [result] = await pool.execute(
        `INSERT INTO issue_materials (
          issue_id, material_type, part_id, material_name,
          material_description, supplier, quantity, unit_of_measure,
          unit_cost, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          issue_id,
          material_type,
          part_id,
          material_name,
          material_description,
          supplier,
          quantity,
          unit_of_measure,
          unit_cost,
          status,
          notes,
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to create issue material: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          im.*,
          p.name as part_name,
          p.part_number,
          p.category as part_category,
          p.manufacturer as part_manufacturer,
          p.quantity_in_stock,
          i.title as issue_title,
          i.status as issue_status
         FROM issue_materials im
         LEFT JOIN parts p ON im.part_id = p.id
         LEFT JOIN issues i ON im.issue_id = i.id
         WHERE im.id = ?`,
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find issue material: ${error.message}`);
    }
  }

  static async findByIssue(issueId) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          im.*,
          p.name as part_name,
          p.part_number,
          p.category as part_category,
          p.manufacturer as part_manufacturer,
          p.quantity_in_stock,
          p.minimum_stock_level
         FROM issue_materials im
         LEFT JOIN parts p ON im.part_id = p.id
         WHERE im.issue_id = ?
         ORDER BY im.created_at ASC`,
        [issueId]
      );

      return rows;
    } catch (error) {
      throw new Error(`Failed to find materials for issue: ${error.message}`);
    }
  }

  static async update(id, materialData) {
    try {
      const {
        material_type,
        part_id = null,
        material_name = null,
        material_description = null,
        supplier = null,
        quantity,
        unit_of_measure,
        unit_cost,
        status,
        notes = null,
      } = materialData;

      // Validate required fields based on material type
      if (material_type === 'stock_part' && !part_id) {
        throw new Error('part_id is required for stock_part type');
      }
      if (material_type === 'external_material' && !material_name) {
        throw new Error('material_name is required for external_material type');
      }

      await pool.execute(
        `UPDATE issue_materials SET
          material_type = ?, part_id = ?, material_name = ?,
          material_description = ?, supplier = ?, quantity = ?,
          unit_of_measure = ?, unit_cost = ?, status = ?, notes = ?
         WHERE id = ?`,
        [
          material_type,
          part_id,
          material_name,
          material_description,
          supplier,
          quantity,
          unit_of_measure,
          unit_cost,
          status,
          notes,
          id,
        ]
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update issue material: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      const [result] = await pool.execute(
        'DELETE FROM issue_materials WHERE id = ?',
        [id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete issue material: ${error.message}`);
    }
  }

  static async getMaterialsSummary(issueId) {
    try {
      const [rows] = await pool.execute(
        `SELECT * FROM issue_materials_summary WHERE issue_id = ?`,
        [issueId]
      );

      return rows[0] || {
        issue_id: issueId,
        total_materials: 0,
        stock_parts_count: 0,
        external_materials_count: 0,
        total_materials_cost: 0,
        stock_parts_cost: 0,
        external_materials_cost: 0,
      };
    } catch (error) {
      throw new Error(`Failed to get materials summary: ${error.message}`);
    }
  }

  static async updateStockQuantities(issueId) {
    try {
      // Get all stock parts used in this issue
      const [materials] = await pool.execute(
        `SELECT im.part_id, im.quantity
         FROM issue_materials im
         WHERE im.issue_id = ? 
         AND im.material_type = 'stock_part' 
         AND im.status = 'used'
         AND im.part_id IS NOT NULL`,
        [issueId]
      );

      // Update stock quantities for each part
      for (const material of materials) {
        await pool.execute(
          `UPDATE parts SET quantity_in_stock = quantity_in_stock - ? WHERE id = ?`,
          [material.quantity, material.part_id]
        );
      }

      return true;
    } catch (error) {
      throw new Error(`Failed to update stock quantities: ${error.message}`);
    }
  }
}
