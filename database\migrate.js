import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';
import { pool } from '../src/config/database.js';

async function migrate() {
  try {
    console.log('🔄 Running database migrations...');

    // Create migrations tracking table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        filename VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Get all migration files
    const migrationsDir = './database/migrations';
    const migrationFiles = readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to ensure correct order

    console.log(`Found ${migrationFiles.length} migration files`);

    for (const file of migrationFiles) {
      // Check if migration already executed
      const [rows] = await pool.execute(
        'SELECT id FROM migrations WHERE filename = ?',
        [file]
      );

      if (rows.length > 0) {
        console.log(`⏭️  Skipping ${file} (already executed)`);
        continue;
      }

      console.log(`🔄 Running migration: ${file}`);

      // Read and execute migration
      const migrationPath = join(migrationsDir, file);
      const migrationSQL = readFileSync(migrationPath, 'utf8');

      // Split by semicolon and execute each statement
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await pool.execute(statement);
          } catch (error) {
            // Some statements might fail if they already exist, that's OK
            if (!error.message.includes('already exists') &&
                !error.message.includes('Duplicate entry') &&
                !error.message.includes('Duplicate key')) {
              console.warn(`⚠️  Warning in ${file}: ${error.message}`);
            }
          }
        }
      }

      // Mark migration as executed
      await pool.execute(
        'INSERT INTO migrations (filename) VALUES (?)',
        [file]
      );

      console.log(`✅ Completed migration: ${file}`);
    }

    console.log('✅ All database migrations completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

migrate();
