# CMMS Kasutajalood ja TDD Testid

## Epic 1: Ma<PERSON>ate Haldus

### US-001: Masina lisamine süsteemi

**<PERSON><PERSON>** administraator  
**<PERSON><PERSON>** lisada uue masina süsteemi  
**Et** saaksin seda hallata ja jälgida

**Acceptance Criteria:**

- <PERSON><PERSON> sisestada masina põhiandmed (number, nimi, tootja, mudel)
- Süsteem genereerib automaatselt QR-koodi
- Masina salvestamisel luuakse andmeba<PERSON> kirje
- QR-kood salvestatakse andmebaasi

**Playwright Test:**

```javascript
test('should add new machine', async ({ page }) => {
  await page.goto('/admin');
  await page.click('[data-testid="add-machine-btn"]');

  // Server-side rendered vorm
  await page.fill('[name="machine_number"]', 'M-001');
  await page.fill('[name="name"]', 'Test Freespink');
  await page.fill('[name="manufacturer"]', 'HAAS');
  await page.fill('[name="model"]', 'VF-2');

  // Traditional form submit või AJAX
  await page.click('[type="submit"]');

  // Server redirect või success message
  await expect(page.locator('.success-message')).toContainText('Masin edukalt lisatud');

  // Kontrolli, et suunatakse masina detailvaatesse
  await expect(page).toHaveURL(/\/machines\/\d+/);
  await expect(page.locator('[data-testid="qr-code"]')).toBeVisible();
});
```

### US-002: Masina QR-koodi printimine

**Kui** administraator  
**Tahan** printida masina QR-koodi  
**Et** saaksin selle masinale kinnitada

**Acceptance Criteria:**

- Näen masina detailvaates QR-koodi
- Saan QR-koodi alla laadida PNG formaadis
- QR-kood sisaldab õiget URL-i operaatori vaatesse

**Playwright Test:**

```javascript
test('should download machine QR code', async ({ page }) => {
  await page.goto('/machines/1');

  // QR-kood on server-side rendered template'is
  await expect(page.locator('[data-testid="qr-code"]')).toBeVisible();

  const downloadPromise = page.waitForDownload();
  await page.click('[data-testid="download-qr"]'); // GET /api/files/qr/1
  const download = await downloadPromise;

  expect(download.suggestedFilename()).toBe('machine_M-001_qr.png');
});
```

### US-003: Masina andmete muutmine

**Kui** administraator  
**Tahan** muuta masina andmeid  
**Et** hoida informatsioon ajakohasena

**Acceptance Criteria:**

- Saan muuta kõiki masina põhiandmeid
- Muudatused salvestatakse andmebaasi
- Süsteem näitab kinnitust edukast salvestamisest

**Playwright Test:**

```javascript
test('should edit machine details', async ({ page }) => {
  await page.goto('/machines/1');

  // Server-side rendered vorm
  await page.click('[data-testid="edit-machine"]');
  await page.fill('[name="name"]', 'Uuendatud Freespink');
  await page.fill('[name="location"]', 'Tsehh A');

  // Traditional form submit
  await page.click('[type="submit"]');

  // Server redirect tagasi masina vaatesse
  await expect(page.locator('.alert-success')).toContainText('Andmed edukalt uuendatud');
  await expect(page.locator('[data-testid="machine-name"]')).toContainText('Uuendatud Freespink');
});
```

## Epic 2: Rikete Registreerimine

### US-004: Operaatori kiire juurdepääs QR-koodi abil

**Kui** operaator  
**Tahan** skannida masina QR-koodi  
**Et** saaksin kiiresti teatada probleemist

**Acceptance Criteria:**

- QR-koodi skanneerimine avab operaatori vaate
- Masina andmed on eeltäidetud
- Vaade on optimeeritud mobiilseadmetele
- Ei ole vaja sisse logida

**Playwright Test:**

```javascript
test('should open operator view via QR code', async ({ page }) => {
  // QR-kood suunab GET /operator/M-001 lehele
  await page.goto('/operator/M-001');

  // EJS template sisaldab masina andmeid serverist
  await expect(page.locator('[data-testid="machine-info"]')).toContainText('M-001');
  await expect(page.locator('[data-testid="machine-name"]')).toContainText('Freespink');

  // Esimene input on focused (progressive enhancement)
  await expect(page.locator('[name="operator_number"]')).toBeFocused();

  // Mobiilsõbralik Tailwind CSS
  await expect(page.locator('body')).toHaveClass(/max-w-md|w-full/);
});
```

### US-005: Rikke registreerimine

**Kui** operaator  
**Tahan** registreerida masina rikke  
**Et** hooldusmeeskond saaks sellest teada

**Acceptance Criteria:**

- Saan sisestada oma operaatori numbri
- Saan valida rikke tüübi
- Saan määrata probleemi tõsiduse
- Saan lisada kirjelduse ja foto
- Rike salvestatakse andmebaasi

**Playwright Test:**

```javascript
test('should register machine issue', async ({ page }) => {
  await page.goto('/operator/M-001');

  // Traditional HTML form (töötab ilma JavaScript'ita)
  await page.fill('[name="operator_number"]', 'OP-123');
  await page.selectOption('[name="issue_type"]', 'mechanical');
  await page.check('[name="severity"][value="high"]');
  await page.fill('[name="description"]', 'Ventilaator teeb kõva häält ja vibreerib');

  // Foto üleslaadimine (multipart/form-data)
  await page.setInputFiles('[name="photo"]', 'test-files/machine-issue.jpg');

  // Form submit -> POST /api/issues
  await page.click('[type="submit"]');

  // Server redirect success lehele või tagasi vormile
  await expect(page.locator('.alert-success')).toContainText('Teade edukalt registreeritud');
  await expect(page.locator('.notification')).toContainText('Hooldusmeeskond on teavitatud');

  // Progressive enhancement: AJAX submit ilma page reload'ita
  await expect(page).toHaveURL('/operator/M-001?success=1');
});
```

### US-006: Hoolduse teatamine

**Kui** operaator  
**Tahan** teatada planeerimata hoolduse vajadusest  
**Et** masin saaks õigeaegselt hooldatud

**Acceptance Criteria:**

- Saan valida "Hooldus" rikke tüübi asemel
- Saan kirjeldada hoolduse vajadust
- Teade läheb hooldusmeeskonnale

**Playwright Test:**

```javascript
test('should report maintenance need', async ({ page }) => {
  await page.goto('/operator/M-001');
  await page.fill('[name="operator_number"]', 'OP-123');

  // Radio button hoolduse kategooria jaoks
  await page.check('[name="issue_category"][value="maintenance"]');
  await page.selectOption('[name="issue_type"]', 'other');
  await page.fill('[name="description"]', 'Õli värvus on muutunud, soovitan vahetada');

  // Traditional form submit
  await page.click('[type="submit"]');

  // Server response
  await expect(page.locator('.alert-info')).toContainText('Hoolduse taotlus registreeritud');
});
```

## Epic 3: Hoolduste Haldus

### US-007: Plaanilise hoolduse loomine

**Kui** hooldusmeeskonna liige  
**Tahan** planeerida regulaarset hooldust  
**Et** vältida ootamatuid rikke

**Acceptance Criteria:**

- Saan valida masina
- Saan määrata hoolduse tüübi ja kuupäeva
- Saan määrata vastutava isiku
- Hooldus salvestatakse kalendrisse

**Playwright Test:**

```javascript
test('should schedule maintenance', async ({ page }) => {
  await page.goto('/maintenance');
  await page.click('[data-testid="new-maintenance"]');

  // Server-side rendered vorm
  await page.selectOption('[name="machine_id"]', '1'); // M-001
  await page.selectOption('[name="maintenance_type"]', 'regular');
  await page.fill('[name="title"]', 'Kvartaalne hooldus');
  await page.fill('[name="scheduled_date"]', '2025-06-15');
  await page.fill('[name="performed_by"]', 'Jaan Tamm');

  // Traditional form submit
  await page.click('[type="submit"]');

  // Redirect tagasi hoolduste nimekirja
  await expect(page).toHaveURL('/maintenance');
  await expect(page.locator('.alert-success')).toContainText('Hooldus edukalt planeeritud');

  // Uus hooldus on tabelis nähtav
  await expect(page.locator('tbody')).toContainText('Kvartaalne hooldus');
});
```

### US-008: Hoolduse märkimine lõpetatuks

**Kui** tehnik  
**Tahan** märkida hoolduse lõpetatuks  
**Et** dokumenteerida tehtud töö

**Acceptance Criteria:**

- Saan lisada hoolduse kirjelduse
- Saan märkida kasutatud varuosad
- Saan sisestada kulutatud aja
- Hooldusajalugu uuendatakse

**Playwright Test:**

```javascript
test('should complete maintenance', async ({ page }) => {
  await page.goto('/maintenance/1');
  await page.click('[data-testid="complete-maintenance"]');

  // Server-side vorm hoolduse lõpetamiseks
  await page.fill(
    '[name="description"]',
    'Vahetasin õli ja filtrid, kontrollisin veorihmade pinge'
  );
  await page.fill('[name="duration_hours"]', '2.5');
  await page.fill('[name="cost"]', '150.00');

  // Varuosade lisamine (JavaScript enhancement)
  await page.click('[data-testid="add-part-btn"]');
  await page.selectOption('[name="parts[0][part_id]"]', '1'); // Õlifiltrid
  await page.fill('[name="parts[0][quantity]"]', '2');

  // Form submit
  await page.click('[type="submit"]');

  // Redirect ja success message
  await expect(page).toHaveURL('/maintenance/1');
  await expect(page.locator('.alert-success')).toContainText('Hooldus märgitud lõpetatuks');
  await expect(page.locator('[data-testid="status"]')).toContainText('Lõpetatud');
});
```

## Epic 4: Varuosade Haldus

### US-009: Varuosa laoseisu jälgimine

**Kui** laohoidja  
**Tahan** jälgida varuosade seisu  
**Et** teada, millal on vaja tellida

**Acceptance Criteria:**

- Näen kõiki varuosi koos kogustega
- Süsteem hoiatab madala varuga osade eest
- Saan uuendada laokoguseid

**Playwright Test:**

```javascript
test('should view parts inventory', async ({ page }) => {
  await page.goto('/parts');

  // Server-side rendered varuosade tabel
  await expect(page.locator('[data-testid="parts-table"]')).toBeVisible();
  await expect(page.locator('tbody')).toContainText('Spindli laager');

  // Madala varuga osade hoiatus (serverist)
  await expect(page.locator('.alert-warning')).toContainText('5 osa vajab tellimist');

  // Filtreerimise vorm
  await page.selectOption('[name="category"]', 'mechanical');
  await page.click('[type="submit"]'); // Traditional form submit

  // Filtered results
  await expect(page).toHaveURL('/parts?category=mechanical');
});
```

### US-010: Varuosa laokoguse uuendamine

**Kui** laohoidja  
**Tahan** uuendada laokogust  
**Et** seis oleks alati täpne

**Acceptance Criteria:**

- Saan muuta osa kogust
- Muudatus salvestatakse kohe
- Kui kogus langeb alla miinimumi, kuvatakse hoiatus

**Playwright Test:**

```javascript
test('should update part quantity', async ({ page }) => {
  await page.goto('/parts');

  // AJAX või traditional form koguse uuendamiseks
  await page.click('[data-testid="edit-part-1"]');
  await page.fill('[name="quantity_in_stock"]', '15');

  // AJAX submit (progressive enhancement)
  await page.click('[data-testid="save-quantity"]');

  // Success message ilma page reload'ita
  await expect(page.locator('.alert-success')).toContainText('Laokogus uuendatud');
  await expect(page.locator('[data-testid="part-1-quantity"]')).toContainText('15');

  // Fallback: traditional form submit toimib samuti
});
```

### US-011: Varuosa seostamine masinaga

**Kui** hooldusmeeskonna liige  
**Tahan** teada, millised osad kuuluvad konkreetsele masinale  
**Et** saaksin õiged osad tellida

**Acceptance Criteria:**

- Masina detailvaates näen vajalikke varuosi
- Saan lisada uusi osi masina juurde
- Näen laoseisu iga osa kohta

**Playwright Test:**

```javascript
test('should manage machine parts', async ({ page }) => {
  await page.goto('/machines/1');
  await page.click('[data-testid="parts-tab"]'); // JavaScript tab switching

  // Uue osa lisamise vorm
  await page.click('[data-testid="add-part-btn"]');
  await page.selectOption('[name="part_id"]', '2'); // Veorihmad
  await page.fill('[name="quantity_needed"]', '1');

  // Traditional form submit
  await page.click('[type="submit"]');

  // Success ja page refresh
  await expect(page.locator('.alert-success')).toContainText('Varuosa lisatud');
  await expect(page.locator('[data-testid="machine-parts-table"]')).toContainText('Veorihmad');
});
```

## Epic 5: Dokumentide Haldus

### US-012: Dokumendi üleslaadimine

**Kui** administraator  
**Tahan** üles laadida masina juhendi  
**Et** see oleks kõigile kättesaadav

**Acceptance Criteria:**

- Saan valida PDF/pildi faili
- Saan määrata dokumendi tüübi
- Dokument salvestatakse andmebaasi
- Dokument kuvatakse masina detailvaates

**Playwright Test:**

```javascript
test('should upload machine document', async ({ page }) => {
  await page.goto('/machines/1');
  await page.click('[data-testid="documents-tab"]');
  await page.click('[data-testid="upload-document-btn"]');

  // Multipart form upload
  await page.setInputFiles('[name="document"]', 'test-files/manual.pdf');
  await page.fill('[name="title"]', 'Kasutusjuhend');
  await page.selectOption('[name="document_type"]', 'manual');

  // Traditional form submit -> POST /api/files/upload
  await page.click('[type="submit"]');

  // Redirect tagasi masina dokumentide vaatesse
  await expect(page).toHaveURL('/machines/1?tab=documents');
  await expect(page.locator('.alert-success')).toContainText('Dokument edukalt lisatud');
  await expect(page.locator('[data-testid="documents-list"]')).toContainText('Kasutusjuhend');
});
```

### US-013: Dokumendi allalaadimine

**Kui** kasutaja  
**Tahan** alla laadida masina juhendi  
**Et** saaksin seda kasutada

**Acceptance Criteria:**

- Saan dokumente vaadata nimekirjas
- Klõpsates dokumendil algab allalaadimine
- Fail laadib alla õige nimega

**Playwright Test:**

```javascript
test('should download machine document', async ({ page }) => {
  await page.goto('/machines/1');
  await page.click('[data-testid="documents-tab"]');

  // Direct link dokumendi allalaadimiseks
  const downloadPromise = page.waitForDownload();
  await page.click('[data-testid="download-doc-1"]'); // GET /api/files/document/1
  const download = await downloadPromise;

  expect(download.suggestedFilename()).toBe('Kasutusjuhend.pdf');
});
```

## Epic 6: Arendusprojektid

### US-014: Arendusprojekti loomine

**Kui** projektijuht  
**Tahan** luua uue arendusprojekti  
**Et** jälgida masinate täiustamist

**Acceptance Criteria:**

- Saan sisestada projekti detailid
- Saan seostada projektiga masinaid
- Projekt kuvatakse projektide nimekirjas

**Playwright Test:**

```javascript
test('should create development project', async ({ page }) => {
  await page.goto('/projects');
  await page.click('[data-testid="new-project-btn"]');

  // Server-side rendered vorm
  await page.fill('[name="title"]', 'CNC tootlikkuse suurendamine');
  await page.fill('[name="description"]', 'Optimeerime lõikamise parameetreid');
  await page.fill('[name="initiator"]', 'Peeter Mets');
  await page.fill('[name="start_date"]', '2025-06-01');
  await page.fill('[name="end_date"]', '2025-12-31');

  // Masinate valimine (checkboxes)
  await page.check('[name="machines[]"][value="1"]'); // M-001
  await page.check('[name="machines[]"][value="2"]'); // M-002

  // Traditional form submit
  await page.click('[type="submit"]');

  // Redirect projektide nimekirja
  await expect(page).toHaveURL('/projects');
  await expect(page.locator('.alert-success')).toContainText('Projekt edukalt loodud');
  await expect(page.locator('tbody')).toContainText('CNC tootlikkuse suurendamine');
});
```

### US-015: Projekti progressi uuendamine

**Kui** projektimeeskonna liige  
**Tahan** uuendada projekti progressi  
**Et** kõik oleksid kursis seisuga

**Acceptance Criteria:**

- Saan muuta progressi protsenti
- Saan lisada kommentaare
- Muudatused kajastuvad kohe

**Playwright Test:**

```javascript
test('should update project progress', async ({ page }) => {
  await page.goto('/projects/1');
  await page.click('[data-testid="update-progress-btn"]');

  // AJAX vorm progressi uuendamiseks
  await page.fill('[name="progress_percentage"]', '75');
  await page.fill('[name="notes"]', 'Lõpetasime esimese faasi testimise');

  // AJAX submit (progressive enhancement)
  await page.click('[data-testid="save-progress"]');

  // Uuendus toimub ilma page reload'ita
  await expect(page.locator('.alert-success')).toContainText('Progress uuendatud');
  await expect(page.locator('[data-testid="progress-bar"]')).toHaveCSS('width', '75%');

  // Fallback: traditional form submit
});
```

## Epic 7: Statistika ja Raportid

### US-016: Rikete statistika vaatamine

**Kui** juht  
**Tahan** näha rikete statistikat  
**Et** mõista, millised masinad vajavad rohkem tähelepanu

**Acceptance Criteria:**

- Näen rikete arvu masinate kaupa
- Näen rikete tüüpide jaotust
- Saan filtreerida ajaperioodi järgi

**Playwright Test:**

```javascript
test('should display issue statistics', async ({ page }) => {
  await page.goto('/admin');
  await page.click('[data-testid="statistics-tab"]'); // JavaScript tab switching

  // Server-side rendered statistika
  await expect(page.locator('[data-testid="issue-chart"]')).toBeVisible();
  await expect(page.locator('h2')).toContainText('Rikete statistika');

  // Ajaperioodi filtreerimine (traditional form)
  await page.selectOption('[name="period"]', 'last-month');
  await page.click('[type="submit"]');

  // Page refresh uute andmetega
  await expect(page).toHaveURL('/admin?tab=statistics&period=last-month');
  await expect(page.locator('[data-testid="chart-title"]')).toContainText('Viimase kuu rikked');
});
```

### US-017: Masinate efektiivsuse ülevaade

**Kui** tootmisjuht  
**Tahan** näha masinate tööaega ja seisakuid  
**Et** planeerida tootmist paremini

**Acceptance Criteria:**

- Näen iga masina tööaja protsenti
- Näen seisakute põhjusi
- Saan võrrelda erinevaid masinaid

**Playwright Test:**

```javascript
test('should show machine efficiency overview', async ({ page }) => {
  await page.goto('/admin');
  await page.click('[data-testid="efficiency-tab"]');

  // Server-side rendered efektiivsuse ülevaade
  await expect(page.locator('[data-testid="efficiency-chart"]')).toBeVisible();
  await expect(page.locator('[data-testid="machine-M-001-uptime"]')).toContainText('87%');

  // Link masina detailvaatesse
  await page.click('[data-testid="machine-M-001-details"]');

  // Navigeerimine masina lehele
  await expect(page).toHaveURL('/machines/1?tab=statistics');
  await expect(page.locator('[data-testid="downtime-reasons"]')).toBeVisible();
});
```

## TDD Arenduse Järjekord

### Faas 1: Põhifunktsioonid (1-2 nädalat)

1. **US-001**: Masina lisamine + QR-koodi genereerimine
2. **US-004**: Operaatori vaade QR-koodi kaudu
3. **US-005**: Rikke registreerimine

### Faas 2: Administraatori töölaud (1 nädal)

4. **US-007**: Hoolduse planeerimine
5. **US-008**: Hoolduse lõpetamine
6. **US-016**: Põhiline statistika

### Faas 3: Varuosad ja dokumendid (1 nädal)

7. **US-009**: Varuosade vaatamine
8. **US-012**: Dokumentide üleslaadimine
9. **US-013**: Dokumentide allalaadimine

### Faas 4: Täiustused (1 nädal)

10. **US-014**: Arendusprojektid
11. **US-017**: Täiustatud statistika
12. **US-003**: Masinate muutmine

## TDD Reeglid

### Iga funktsiooni jaoks:

1. **🔴 RED**: Kirjuta Playwright test, mis ebaõnnestub
2. **🟢 GREEN**: Kirjuta minimaalne kood testi möödamiseks
3. **🔵 REFACTOR**: Paranda koodi, hoides testi rohelisena

### Testide järjekord iga funktsiooni jaoks:

1. **E2E test** (Playwright) - kasutaja vaatest
2. **API test** (Vitest) - backend endpoint
3. **Unit test** (Vitest) - üksikud funktsioonid

### Näide TDD tsüklist US-001 jaoks:

#### Samm 1: E2E Test (ebaõnnestub)

```javascript
test('should add new machine with server-side rendering', async ({ page }) => {
  await page.goto('/admin');
  await page.click('[data-testid="add-machine-btn"]');

  // Traditional HTML form
  await page.fill('[name="machine_number"]', 'M-001');
  await page.fill('[name="name"]', 'Test Freespink');
  await page.click('[type="submit"]');

  // Server redirect
  await expect(page).toHaveURL(/\/machines\/\d+/);
  await expect(page.locator('.alert-success')).toBeVisible();
});
```

#### Samm 2: API Test (ebaõnnestub)

```javascript
test('POST /api/machines should create machine', async () => {
  const response = await request(app)
    .post('/api/machines')
    .send({ machine_number: 'M-001', name: 'Test' });
  expect(response.status).toBe(201);
  expect(response.body).toHaveProperty('id');
});
```

#### Samm 3: Implementeeri backend

```javascript
// Traditional form handler
app.post('/machines', async (req, res) => {
  const machine = await createMachine(req.body);
  res.redirect(`/machines/${machine.id}?success=created`);
});

// API endpoint
app.post('/api/machines', async (req, res) => {
  const machine = await createMachine(req.body);
  res.status(201).json(machine);
});
```

#### Samm 4: Implementeeri frontend (EJS template)

```html
<!-- views/machines/new.ejs -->
<form action="/machines" method="POST">
  <input name="machine_number" required />
  <input name="name" required />
  <button type="submit">Lisa masin</button>
</form>
```

#### Samm 5: Progressive Enhancement

```javascript
// public/js/forms.js - AJAX enhancement
document.addEventListener('DOMContentLoaded', function () {
  const forms = document.querySelectorAll('form[data-ajax]');
  forms.forEach(form => {
    form.addEventListener('submit', handleAjaxSubmit);
  });
});
```

## Fullstack TDD Lähenemise Eelised

### ✅ **Lihtsam Testimine**

- **Üks server**: ei ole vaja kahte erinevat protsessi
- **Traditional forms**: toimivad alati, ka kui JavaScript ebaõnnestub
- **Progressive enhancement**: algab HTML/CSS-ist, lisab JS funktsionaalsust

### ✅ **Kiire Development Loop**

```bash
# 1. Kirjuta Playwright test
bun run test:e2e -- --headed

# 2. Implementeeri EJS template + route
# 3. Test muutub roheliseks

# 4. Lisa AJAX enhancement (valikuline)
# 5. Test jääb roheliseks
```

### ✅ **Graceful Degradation**

- **HTML forms** - töötab kõikjal
- **CSS styling** - Tailwind responsive design
- **JavaScript** - parandab kasutajakogemust, aga ei ole vajalik

### ✅ **Real-world Testing**

```javascript
// Test töötab nii JavaScript'iga kui ilma
test('form works with and without JS', async ({ page }) => {
  // Disable JavaScript
  await page.context().addInitScript('window.javascript_enabled = false');

  // Test traditional form submit
  await page.goto('/operator/M-001');
  await page.fill('[name="operator_number"]', 'OP-123');
  await page.click('[type="submit"]');

  // Should still work
  await expect(page.locator('.alert-success')).toBeVisible();
});
```

See tagab, et CMMS rakendus töötab kõigis tingimustes ja on tõeliselt robust!

## Epic 8: Turvalisus ja Kasutajahaldus

### US-018: Administraatori sisselogimine

**Kui** administraator
**Tahan** turvaliselt sisse logida
**Et** pääseda ligi tundlikele funktsioonidele

**Acceptance Criteria:**

- Saan sisestada kasutajanime ja parooli
- Parool on krüpteeritud andmebaasis
- Ebaõnnestunud sisselogimised logitakse
- Sessioon aegub pärast tegevusetust
- Saan välja logida

**Playwright Test:**

```javascript
test('should login as administrator', async ({ page }) => {
  await page.goto('/admin');

  // Redirect to login page if not authenticated
  await expect(page).toHaveURL('/login');

  // Traditional login form
  await page.fill('[name="username"]', 'admin');
  await page.fill('[name="password"]', 'admin123');
  await page.click('[type="submit"]');

  // Successful login redirects to admin dashboard
  await expect(page).toHaveURL('/admin');
  await expect(page.locator('[data-testid="admin-header"]')).toContainText('Administraatori töölaud');

  // Logout functionality
  await page.click('[data-testid="logout-btn"]');
  await expect(page).toHaveURL('/login');
});
```

### US-019: Ebaõnnestunud sisselogimise käsitlemine

**Kui** keegi
**Tahan** et süsteem oleks kaitstud väärkasutuse eest
**Et** andmed oleksid turvalised

**Acceptance Criteria:**

- Vale parool näitab veateadet
- Liiga palju ebaõnnestunud katseid blokeerib IP
- Parool peab vastama turvalisuse nõuetele
- Sessioon kustutatakse väljalogimisel

**Playwright Test:**

```javascript
test('should handle failed login attempts', async ({ page }) => {
  await page.goto('/login');

  // Wrong password
  await page.fill('[name="username"]', 'admin');
  await page.fill('[name="password"]', 'wrongpassword');
  await page.click('[type="submit"]');

  // Should stay on login page with error
  await expect(page).toHaveURL('/login');
  await expect(page.locator('.alert-error')).toContainText('Vale kasutajanimi või parool');

  // Multiple failed attempts
  for (let i = 0; i < 5; i++) {
    await page.fill('[name="password"]', 'wrong' + i);
    await page.click('[type="submit"]');
  }

  // Should be temporarily blocked
  await expect(page.locator('.alert-warning')).toContainText('Liiga palju ebaõnnestunud katseid');
});
```

### US-020: Kasutajate haldamine

**Kui** peaadministraator
**Tahan** luua ja hallata kasutajakontosid
**Et** anda erinevaid õigusi erinevatele inimestele

**Acceptance Criteria:**

- Saan luua uusi kasutajaid
- Saan määrata rolle (Admin, Hooldaja, Operaator, Vaatleja)
- Saan muuta kasutaja andmeid ja rolli
- Saan kasutajaid deaktiveerida
- Näen kasutajate viimast sisselogimist

**Playwright Test:**

```javascript
test('should manage user accounts', async ({ page }) => {
  // Login as admin
  await page.goto('/login');
  await page.fill('[name="username"]', 'admin');
  await page.fill('[name="password"]', 'admin123');
  await page.click('[type="submit"]');

  // Navigate to user management
  await page.goto('/admin/users');
  await page.click('[data-testid="add-user-btn"]');

  // Create new user
  await page.fill('[name="username"]', 'jaan.tamm');
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="full_name"]', 'Jaan Tamm');
  await page.selectOption('[name="role"]', 'maintenance');
  await page.fill('[name="password"]', 'TempPass123!');
  await page.fill('[name="password_confirm"]', 'TempPass123!');

  await page.click('[type="submit"]');

  // Should redirect to users list
  await expect(page).toHaveURL('/admin/users');
  await expect(page.locator('.alert-success')).toContainText('Kasutaja edukalt loodud');
  await expect(page.locator('tbody')).toContainText('Jaan Tamm');
});
```

### US-021: Rollipõhine juurdepääs

**Kui** süsteemi kasutaja
**Tahan** et näeksin ainult mulle lubatud funktsioone
**Et** süsteem oleks turvaline ja lihtne kasutada

**Acceptance Criteria:**

- **Admin**: Täielik juurdepääs kõigile funktsioonidele
- **Hooldaja**: Hoolduste ja rikete haldamine, varuosade vaatamine
- **Operaator**: Ainult rikete teatamine (QR-koodi kaudu)
- **Vaatleja**: Ainult lugemisõigus aruannetele ja statistikale

**Playwright Test:**

```javascript
test('should enforce role-based access control', async ({ page }) => {
  // Test maintenance user access
  await page.goto('/login');
  await page.fill('[name="username"]', 'jaan.tamm');
  await page.fill('[name="password"]', 'TempPass123!');
  await page.click('[type="submit"]');

  // Should redirect to maintenance dashboard
  await expect(page).toHaveURL('/maintenance');

  // Should see maintenance functions
  await expect(page.locator('[data-testid="maintenance-nav"]')).toBeVisible();
  await expect(page.locator('[data-testid="issues-nav"]')).toBeVisible();

  // Should NOT see admin functions
  await expect(page.locator('[data-testid="users-nav"]')).not.toBeVisible();
  await expect(page.locator('[data-testid="system-settings"]')).not.toBeVisible();

  // Direct access to admin pages should be blocked
  await page.goto('/admin/users');
  await expect(page).toHaveURL('/unauthorized');
  await expect(page.locator('.alert-error')).toContainText('Puuduvad õigused');
});
```

### US-022: Parooli muutmine

**Kui** kasutaja
**Tahan** muuta oma parooli
**Et** hoida oma konto turvalisena

**Acceptance Criteria:**

- Saan muuta oma parooli profiili lehel
- Pean sisestama vana parooli kinnituseks
- Uus parool peab vastama turvalisuse nõuetele
- Parool krüpteeritakse enne salvestamist

**Playwright Test:**

```javascript
test('should change user password', async ({ page }) => {
  // Login
  await page.goto('/login');
  await page.fill('[name="username"]', 'jaan.tamm');
  await page.fill('[name="password"]', 'TempPass123!');
  await page.click('[type="submit"]');

  // Go to profile
  await page.click('[data-testid="profile-menu"]');
  await page.click('[data-testid="change-password"]');

  // Change password form
  await page.fill('[name="current_password"]', 'TempPass123!');
  await page.fill('[name="new_password"]', 'NewSecure456!');
  await page.fill('[name="confirm_password"]', 'NewSecure456!');
  await page.click('[type="submit"]');

  // Success message
  await expect(page.locator('.alert-success')).toContainText('Parool edukalt muudetud');

  // Logout and test new password
  await page.click('[data-testid="logout-btn"]');
  await page.fill('[name="username"]', 'jaan.tamm');
  await page.fill('[name="password"]', 'NewSecure456!');
  await page.click('[type="submit"]');

  await expect(page).toHaveURL('/maintenance');
});
```

### US-023: Kasutajate aktiivsuse jälgimine

**Kui** administraator
**Tahan** näha kasutajate aktiivsust
**Et** jälgida süsteemi kasutamist ja turvalisust

**Acceptance Criteria:**

- Näen kasutajate viimast sisselogimist
- Näen aktiivseid sessioone
- Saan vaadata sisselogimiste ajalugu
- Saan sundida kasutajat välja logima

**Playwright Test:**

```javascript
test('should track user activity', async ({ page }) => {
  // Login as admin
  await page.goto('/login');
  await page.fill('[name="username"]', 'admin');
  await page.fill('[name="password"]', 'admin123');
  await page.click('[type="submit"]');

  // View user activity
  await page.goto('/admin/users');
  await page.click('[data-testid="user-activity-tab"]');

  // Should see login history
  await expect(page.locator('[data-testid="login-history"]')).toBeVisible();
  await expect(page.locator('tbody')).toContainText('jaan.tamm');

  // Should see active sessions
  await expect(page.locator('[data-testid="active-sessions"]')).toBeVisible();

  // Force logout user
  await page.click('[data-testid="force-logout-jaan.tamm"]');
  await expect(page.locator('.alert-success')).toContainText('Kasutaja sunnitud välja logima');
});
```

### US-024: Operaatori QR-koodi juurdepääs ilma sisselogimiseta

**Kui** operaator
**Tahan** QR-koodi kaudu kiiresti rikke teatada
**Et** ei peaks iga kord sisse logima

**Acceptance Criteria:**

- QR-koodi skanneerimine ei nõua sisselogimist
- Operaatori vaade on avalik, aga piiratud
- Saab teatada ainult rikkeid, mitte vaadata tundlikku infot
- Teated salvestatakse anonüümselt või operaatori numbriga

**Playwright Test:**

```javascript
test('should allow anonymous issue reporting via QR', async ({ page }) => {
  // QR code should work without login
  await page.goto('/operator/M-001');

  // Should NOT redirect to login
  await expect(page).toHaveURL('/operator/M-001');
  await expect(page.locator('[data-testid="machine-info"]')).toContainText('M-001');

  // Can report issue without authentication
  await page.fill('[name="operator_number"]', 'OP-123');
  await page.selectOption('[name="issue_type"]', 'mechanical');
  await page.fill('[name="description"]', 'Masin teeb kõva häält');
  await page.click('[type="submit"]');

  await expect(page.locator('.alert-success')).toContainText('Rike edukalt registreeritud');

  // But cannot access admin functions
  await page.goto('/admin');
  await expect(page).toHaveURL('/login');
});
```

### US-027: Turvaline statsionaarne QR-kood

**Kui** operaator
**Tahan** kasutada masina küljes olevat QR-koodi turvaliselt
**Et** kiiresti rikke teatada ilma turvalisuse ohustamiseta

**Acceptance Criteria:**

- QR-kood sisaldab masina-spetsiifilist saladust URL-is
- QR-kood on statsionaarne (ei muutu kunagi)
- Vale saladusega QR-kood näitab 404 viga
- Rate limiting takistab liigset kasutamist (max 10 korda 15 minutis)
- Juurdepääs ainult ettevõtte sisevõrgust
- Kõik QR-koodi kasutamised logitakse turvalisuse jälgimiseks
- Kahtlane tegevus (vale saladused) logitakse eraldi

**Playwright Test:**

```javascript
test('should use secure stationary QR codes with machine secrets', async ({ page }) => {
  // Valid QR code with correct machine secret should work
  await page.goto('/secure/M-001/a1b2c3d4e5f6');
  await expect(page).toHaveURL('/operator/M-001');
  await expect(page.locator('[data-testid="machine-info"]')).toContainText('M-001');

  // Invalid secret should show 404 error
  await page.goto('/secure/M-001/wrongsecret123');
  await expect(page).toHaveURL('/secure/M-001/wrongsecret123');
  await expect(page.locator('.alert-error')).toContainText('QR-kood ei ole kehtiv');

  // Different machine with wrong secret should fail
  await page.goto('/secure/M-002/a1b2c3d4e5f6'); // M-001's secret for M-002
  await expect(page.locator('.alert-error')).toContainText('QR-kood ei ole kehtiv');

  // Rate limiting should work after too many attempts
  for (let i = 0; i < 12; i++) {
    await page.goto('/secure/M-001/a1b2c3d4e5f6');
  }
  await expect(page.locator('.alert-error')).toContainText('Liiga palju katseid');

  // Should be able to report issue after valid QR scan
  await page.goto('/secure/M-001/a1b2c3d4e5f6');
  await page.fill('[name="operator_number"]', 'OP-456');
  await page.selectOption('[name="issue_type"]', 'electrical');
  await page.fill('[name="description"]', 'Elektriline probleem');
  await page.click('[type="submit"]');

  await expect(page.locator('.alert-success')).toContainText('Rike edukalt registreeritud');
});

test('should log QR code access attempts', async ({ page }) => {
  // Valid access should be logged
  await page.goto('/secure/M-001/a1b2c3d4e5f6');

  // Invalid access should be logged as suspicious
  await page.goto('/secure/M-001/hacker_attempt');

  // Admin should see access logs
  await page.goto('/login');
  await page.fill('[name="username"]', 'admin');
  await page.fill('[name="password"]', 'admin123');
  await page.click('[type="submit"]');

  await page.goto('/admin/security/qr-logs');
  await expect(page.locator('tbody')).toContainText('M-001');
  await expect(page.locator('tbody')).toContainText('Valid access');
  await expect(page.locator('tbody')).toContainText('Invalid secret attempt');
});
```

## Epic 9: Turvalisuse täiustused

### US-025: Kaheastmeline autentimine (2FA)

**Kui** administraator
**Tahan** kasutada kaheastmelist autentimist
**Et** suurendada konto turvalisust

**Acceptance Criteria:**

- Saan lubada 2FA oma kontol
- Kasutan TOTP rakendust (Google Authenticator)
- Sisselogimisel küsitakse 6-kohaline kood
- Saan genereerida backup koodid

**Playwright Test:**

```javascript
test('should setup and use 2FA', async ({ page }) => {
  // Login and go to security settings
  await page.goto('/login');
  await page.fill('[name="username"]', 'admin');
  await page.fill('[name="password"]', 'admin123');
  await page.click('[type="submit"]');

  await page.goto('/profile/security');
  await page.click('[data-testid="enable-2fa"]');

  // Should show QR code for TOTP setup
  await expect(page.locator('[data-testid="totp-qr"]')).toBeVisible();
  await expect(page.locator('[data-testid="backup-codes"]')).toBeVisible();

  // Simulate entering TOTP code
  await page.fill('[name="totp_code"]', '123456');
  await page.click('[data-testid="verify-2fa"]');

  await expect(page.locator('.alert-success')).toContainText('2FA edukalt lubatud');
});
```

### US-026: Audit log

**Kui** administraator
**Tahan** näha kõiki süsteemis tehtud muudatusi
**Et** jälgida turvalisust ja vastutust

**Acceptance Criteria:**

- Kõik olulised tegevused logitakse
- Näen kes, millal ja mida muutis
- Saan filtreerida logisid kasutaja ja tegevuse järgi
- Logid on kaitstud muutmise eest

**Playwright Test:**

```javascript
test('should log all important actions', async ({ page }) => {
  // Login as admin
  await page.goto('/login');
  await page.fill('[name="username"]', 'admin');
  await page.fill('[name="password"]', 'admin123');
  await page.click('[type="submit"]');

  // Perform some action that should be logged
  await page.goto('/machines/1/edit');
  await page.fill('[name="name"]', 'Uuendatud masin');
  await page.click('[type="submit"]');

  // Check audit log
  await page.goto('/admin/audit');
  await expect(page.locator('tbody')).toContainText('Machine updated');
  await expect(page.locator('tbody')).toContainText('admin');
  await expect(page.locator('tbody')).toContainText('Uuendatud masin');
});
```

## Turvalisuse implementeerimise järjekord

### Faas 1: Põhiline autentimine (1 nädal)
1. **US-018**: Admin sisselogimine
2. **US-019**: Ebaõnnestunud sisselogimiste käsitlemine
3. **US-024**: Operaatori QR juurdepääs
4. **US-027**: Turvaline statsionaarne QR-kood

### Faas 2: Kasutajahaldus (1 nädal)
5. **US-020**: Kasutajate loomine ja haldamine
6. **US-021**: Rollipõhine juurdepääs
7. **US-022**: Parooli muutmine

### Faas 3: Täiustatud turvalisus (1 nädal)
8. **US-023**: Kasutajate aktiivsuse jälgimine
9. **US-025**: Kaheastmeline autentimine
10. **US-026**: Audit log

## Rollide määratlus

### 🔴 **Admin** (Täielik juurdepääs)
- Kasutajate haldamine
- Süsteemi seadistused
- Kõik CMMS funktsioonid
- Audit logid

### 🔧 **Hooldaja** (Maintenance)
- Hoolduste planeerimine ja täitmine
- Rikete lahendamine
- Varuosade haldamine
- Masinate andmete muutmine

### 👷 **Operaator** (Operator)
- Rikete teatamine QR-koodi kaudu
- Hoolduse vajaduse teatamine
- Ainult lugemisõigus oma teadetele

### 👁️ **Vaatleja** (Viewer)
- Ainult lugemisõigus
- Aruannete vaatamine
- Statistika vaatamine
- Ei saa midagi muuta

## Turvalisuse nõuded

### Paroolide nõuded:
- Vähemalt 8 tähemärki
- Sisaldab suuri ja väikesi tähti
- Sisaldab numbreid
- Sisaldab erimärke
- Ei tohi olla levinud paroolide nimekirjas

### Sessiooni turvalisus:
- Sessioon aegub 8 tunni pärast
- Automaatne väljalogimine tegevusetuse korral
- Secure cookie'd HTTPS-i korral
- CSRF kaitse vormidel

### Andmebaasi turvalisus:
- Paroolid bcrypt krüpteeringuga
- SQL injection kaitse
- Prepared statements
- Andmebaasi kasutaja piiratud õigustega
