import { pool } from '../config/database.js';

export class MaintenancePartner {
  static async create(partnerData) {
    try {
      const {
        company_name,
        contact_person,
        email,
        phone,
        address,
        specializations,
        hourly_rate,
        currency = 'EUR',
        notes,
      } = partnerData;

      const [result] = await pool.execute(
        `INSERT INTO maintenance_partners
         (company_name, contact_person, email, phone, address, specializations, hourly_rate, currency, notes)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          company_name,
          contact_person,
          email,
          phone,
          address,
          JSON.stringify(specializations || []),
          hourly_rate,
          currency,
          notes,
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to create maintenance partner: ${error.message}`);
    }
  }

  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT
          mp.*,
          COUNT(DISTINCT mr.id) as active_maintenance_requests,
          COUNT(DISTINCT i.id) as active_issues
        FROM maintenance_partners mp
        LEFT JOIN maintenance_requests mr ON mp.id = mr.partner_id
          AND mr.status IN ('requested', 'scheduled', 'in_progress')
        LEFT JOIN issues i ON mp.id = i.partner_id
          AND i.status IN ('open', 'in_progress')
      `;

      const conditions = [];
      const params = [];

      if (filters.is_active !== undefined) {
        conditions.push('mp.is_active = ?');
        params.push(filters.is_active);
      }

      if (filters.specialization) {
        conditions.push('JSON_CONTAINS(mp.specializations, ?)');
        params.push(JSON.stringify(filters.specialization));
      }

      if (filters.search) {
        conditions.push('(mp.company_name LIKE ? OR mp.contact_person LIKE ? OR mp.email LIKE ?)');
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' GROUP BY mp.id ORDER BY mp.company_name ASC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);

      // Parse specializations JSON and calculate total active requests
      return rows.map(row => ({
        ...row,
        specializations: JSON.parse(row.specializations || '[]'),
        active_requests: (parseInt(row.active_maintenance_requests) || 0) + (parseInt(row.active_issues) || 0),
      }));
    } catch (error) {
      throw new Error(`Failed to fetch maintenance partners: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          mp.*,
          COUNT(DISTINCT mr.id) as active_maintenance_requests,
          COUNT(DISTINCT i.id) as active_issues
         FROM maintenance_partners mp
         LEFT JOIN maintenance_requests mr ON mp.id = mr.partner_id
           AND mr.status IN ('requested', 'scheduled', 'in_progress')
         LEFT JOIN issues i ON mp.id = i.partner_id
           AND i.status IN ('open', 'in_progress')
         WHERE mp.id = ?
         GROUP BY mp.id`,
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      const partner = rows[0];
      partner.specializations = JSON.parse(partner.specializations || '[]');
      partner.active_requests = (parseInt(partner.active_maintenance_requests) || 0) + (parseInt(partner.active_issues) || 0);

      return partner;
    } catch (error) {
      throw new Error(`Failed to find maintenance partner: ${error.message}`);
    }
  }

  static async findByEmail(email) {
    try {
      const [rows] = await pool.execute('SELECT * FROM maintenance_partners WHERE email = ?', [
        email,
      ]);

      if (rows.length === 0) {
        return null;
      }

      const partner = rows[0];
      partner.specializations = JSON.parse(partner.specializations || '[]');

      return partner;
    } catch (error) {
      throw new Error(`Failed to find maintenance partner by email: ${error.message}`);
    }
  }

  static async update(id, updateData) {
    try {
      const fields = [];
      const params = [];

      // Build dynamic update query
      for (const [key, value] of Object.entries(updateData)) {
        if (value !== undefined) {
          if (key === 'specializations') {
            fields.push(`${key} = ?`);
            params.push(JSON.stringify(value));
          } else {
            fields.push(`${key} = ?`);
            params.push(value);
          }
        }
      }

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      params.push(id);

      await pool.execute(
        `UPDATE maintenance_partners SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        params
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update maintenance partner: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      // Check if partner has active maintenance requests
      const [activeRequests] = await pool.execute(
        'SELECT COUNT(*) as count FROM maintenance_requests WHERE partner_id = ? AND status IN ("requested", "scheduled", "in_progress")',
        [id]
      );

      if (activeRequests[0].count > 0) {
        throw new Error('Cannot delete partner with active maintenance requests');
      }

      const [result] = await pool.execute('DELETE FROM maintenance_partners WHERE id = ?', [id]);

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete maintenance partner: ${error.message}`);
    }
  }

  static async deactivate(id) {
    try {
      return await this.update(id, { is_active: false });
    } catch (error) {
      throw new Error(`Failed to deactivate maintenance partner: ${error.message}`);
    }
  }

  static async activate(id) {
    try {
      return await this.update(id, { is_active: true });
    } catch (error) {
      throw new Error(`Failed to activate maintenance partner: ${error.message}`);
    }
  }

  static async getStatistics() {
    try {
      const [rows] = await pool.execute(`
        SELECT
          COUNT(*) as total_partners,
          SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_partners,
          SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_partners,
          AVG(hourly_rate) as average_hourly_rate,
          COUNT(DISTINCT JSON_EXTRACT(specializations, '$[*]')) as total_specializations
        FROM maintenance_partners
      `);

      // Get maintenance requests statistics
      const [requestStats] = await pool.execute(`
        SELECT
          COUNT(*) as total_maintenance_requests,
          SUM(CASE WHEN mr.status = 'requested' THEN 1 ELSE 0 END) as pending_maintenance_requests,
          SUM(CASE WHEN mr.status = 'scheduled' THEN 1 ELSE 0 END) as scheduled_maintenance_requests,
          SUM(CASE WHEN mr.status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_maintenance_requests,
          SUM(CASE WHEN mr.status = 'completed' THEN 1 ELSE 0 END) as completed_maintenance_requests,
          SUM(CASE WHEN mr.status IN ('requested', 'scheduled', 'in_progress') THEN 1 ELSE 0 END) as active_maintenance_requests,
          AVG(mr.partner_cost) as average_partner_cost
        FROM maintenance_requests mr
        WHERE mr.partner_id IS NOT NULL
      `);

      // Get issues statistics
      const [issueStats] = await pool.execute(`
        SELECT
          COUNT(*) as total_partner_issues,
          SUM(CASE WHEN i.status = 'open' THEN 1 ELSE 0 END) as open_partner_issues,
          SUM(CASE WHEN i.status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_partner_issues,
          SUM(CASE WHEN i.status = 'resolved' THEN 1 ELSE 0 END) as resolved_partner_issues,
          SUM(CASE WHEN i.status IN ('open', 'in_progress') THEN 1 ELSE 0 END) as active_partner_issues
        FROM issues i
        WHERE i.partner_id IS NOT NULL
      `);

      return {
        ...rows[0],
        ...requestStats[0],
        ...issueStats[0],
        // Keep original total_partner_requests for all maintenance requests
        total_partner_requests: parseInt(requestStats[0].total_maintenance_requests) || 0,
        // Add new field for active requests only (maintenance + issues)
        active_partner_requests: (parseInt(requestStats[0].active_maintenance_requests) || 0) + (parseInt(issueStats[0].active_partner_issues) || 0),
      };
    } catch (error) {
      throw new Error(`Failed to get maintenance partner statistics: ${error.message}`);
    }
  }

  static async getPartnerRequests(partnerId, filters = {}) {
    try {
      let query = `
        SELECT
          mr.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location
        FROM maintenance_requests mr
        JOIN machines m ON mr.machine_id = m.id
        WHERE mr.partner_id = ?
      `;

      const params = [partnerId];

      if (filters.status) {
        query += ' AND mr.status = ?';
        params.push(filters.status);
      }

      query += ' ORDER BY mr.created_at DESC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to get partner requests: ${error.message}`);
    }
  }

  static async getPartnerIssues(partnerId, filters = {}) {
    try {
      let query = `
        SELECT
          i.*,
          m.machine_number,
          m.name as machine_name,
          m.location as machine_location
        FROM issues i
        JOIN machines m ON i.machine_id = m.id
        WHERE i.partner_id = ?
      `;

      const params = [partnerId];

      if (filters.status) {
        query += ' AND i.status = ?';
        params.push(filters.status);
      }

      query += ' ORDER BY i.created_at DESC';

      if (filters.limit) {
        query += ` LIMIT ${parseInt(filters.limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to get partner issues: ${error.message}`);
    }
  }

  static async getAvailableSpecializations() {
    try {
      const specializations = [
        'mechanical',
        'electrical',
        'hydraulic',
        'pneumatic',
        'automation',
        'calibration',
        'inspection',
        'cleaning',
        'emergency',
        'preventive',
        'corrective',
        'testing',
        'installation',
        'repair',
        'maintenance',
      ];

      return specializations;
    } catch (error) {
      throw new Error(`Failed to get available specializations: ${error.message}`);
    }
  }
}
