import { Hono } from 'hono';
import { requireAuth } from '../../middleware/auth.js';
import { User } from '../../models/index.js';

const profileApiRoutes = new Hono();

// Change password
profileApiRoutes.post('/change-password', requireAuth, async c => {
  try {
    const body = await c.req.json();
    const { current_password, new_password, confirm_password } = body;
    const user = c.get('user');

    // Validate required fields
    if (!current_password || !new_password || !confirm_password) {
      return c.json({
        success: false,
        message: 'Kõik väljad on kohustuslikud'
      }, 400);
    }

    // Validate passwords match
    if (new_password !== confirm_password) {
      return c.json({
        success: false,
        message: 'Uued paroolid ei kattu'
      }, 400);
    }

    // Validate current password
    const isValidCurrentPassword = await user.validatePassword(current_password);
    if (!isValidCurrentPassword) {
      return c.json({
        success: false,
        message: '<PERSON>raegune parool on vale'
      }, 400);
    }

    // Validate new password strength
    const passwordValidation = User.validatePasswordStrength(new_password);
    if (!passwordValidation.isValid) {
      return c.json({
        success: false,
        message: passwordValidation.errors.join(', ')
      }, 400);
    }

    // Check if new password is different from current
    const isSamePassword = await user.validatePassword(new_password);
    if (isSamePassword) {
      return c.json({
        success: false,
        message: 'Uus parool peab erinema praegusest paroolist'
      }, 400);
    }

    // Hash new password
    const newPasswordHash = await User.hashPassword(new_password);

    // Update user password
    await user.update({ password_hash: newPasswordHash });

    return c.json({
      success: true,
      message: 'Parool edukalt muudetud'
    });

  } catch (error) {
    console.error('Error changing password:', error);
    return c.json({
      success: false,
      message: 'Viga parooli muutmisel'
    }, 500);
  }
});

// Get current user profile
profileApiRoutes.get('/', requireAuth, async c => {
  try {
    const user = c.get('user');

    return c.json({
      success: true,
      user: {
        id: user.id,
        full_name: user.full_name,
        username: user.username,
        email: user.email,
        role: user.role,
        last_login_at: user.last_login_at,
        created_at: user.created_at
      }
    });

  } catch (error) {
    console.error('Error getting profile:', error);
    return c.json({
      success: false,
      message: 'Viga profiili andmete laadimisel'
    }, 500);
  }
});

export { profileApiRoutes };
