<!doctype html>
<html lang="et">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QR-koodide regenereerimine - CMMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center">
      <div class="bg-white rounded-lg shadow-md p-8 max-w-md w-full">
        <div class="text-center mb-6">
          <div
            class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"
          >
            <i class="fas fa-qrcode text-blue-600 text-2xl"></i>
          </div>
          <h1 class="text-2xl font-bold text-gray-800">QR-koodide regenereerimine</h1>
          <p class="text-gray-600 mt-2">Uuenda kõik QR-koodid uue BASE_URL-iga</p>
        </div>

        <div class="space-y-4">
          <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  <strong>Hoiatus:</strong> See uuendab kõik QR-koodid uue BASE_URL-iga:
                  <code class="bg-yellow-100 px-1 rounded">http://10.0.11.194:8080</code>
                </p>
              </div>
            </div>
          </div>

          <button
            onclick="regenerateQRCodes()"
            id="regenerate-btn"
            class="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            <i class="fas fa-sync-alt mr-2"></i>Regenereeri kõik QR-koodid
          </button>

          <div id="result" class="hidden"></div>

          <div class="text-center">
            <a href="http://localhost:8080/admin" class="text-blue-600 hover:text-blue-800 text-sm">
              ← Tagasi administraatori vaatesse
            </a>
          </div>
        </div>
      </div>
    </div>

    <script>
      async function regenerateQRCodes() {
        const button = document.getElementById('regenerate-btn');
        const result = document.getElementById('result');

        // Disable button and show loading
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Regenereerin...';
        button.className = 'w-full bg-gray-500 text-white py-3 px-4 rounded-md cursor-not-allowed';

        // Hide previous result
        result.classList.add('hidden');

        try {
          const response = await fetch('http://localhost:8080/api/machines/regenerate-all-qr', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          const data = await response.json();

          if (response.ok) {
            result.innerHTML = `
                        <div class="bg-green-50 border border-green-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-green-700">
                                        <strong>Edukas!</strong> ${data.message}
                                    </p>
                                </div>
                            </div>
                        </div>
                    `;
          } else {
            result.innerHTML = `
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-red-700">
                                        <strong>Viga:</strong> ${data.error}
                                    </p>
                                </div>
                            </div>
                        </div>
                    `;
          }
        } catch (error) {
          result.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-700">
                                    <strong>Viga:</strong> ${error.message}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
        } finally {
          // Re-enable button
          button.disabled = false;
          button.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>Regenereeri kõik QR-koodid';
          button.className =
            'w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors';

          // Show result
          result.classList.remove('hidden');
        }
      }
    </script>
  </body>
</html>
