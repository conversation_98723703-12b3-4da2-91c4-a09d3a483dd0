<!DOCTYPE html>
<html lang="et">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Koodi Turvalisuse Haldus - CMMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-shield-alt text-blue-600 mr-3"></i>
                        QR Koodi Turvalisuse Haldus
                    </h1>
                    <p class="mt-2 text-gray-600">Halda masinate QR koodide turvalisust ja secret-e</p>
                </div>
            </div>

            <!-- Status Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <i class="fas fa-qrcode text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Kokku masinaid</h3>
                            <p class="text-2xl font-bold text-blue-600" id="total-machines">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <i class="fas fa-key text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Turvalised QR koodid</h3>
                            <p class="text-2xl font-bold text-green-600" id="secure-machines">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Vajab parandamist</h3>
                            <p class="text-2xl font-bold text-red-600" id="insecure-machines">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Toimingud</h2>
                </div>
                <div class="p-6">
                    <div class="flex flex-wrap gap-4">
                        <button 
                            id="check-status-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-search mr-2"></i>
                            Kontrolli olukorda
                        </button>
                        
                        <button 
                            id="ensure-secrets-btn"
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-key mr-2"></i>
                            Genereeri puuduvad secret-id
                        </button>
                        
                        <button 
                            id="regenerate-qr-btn"
                            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-sync mr-2"></i>
                            Regenereeri kõik QR koodid
                        </button>
                        
                        <button 
                            id="test-qr-btn"
                            class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-flask mr-2"></i>
                            Testi QR URL-e
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Tulemused</h2>
                </div>
                <div class="p-6">
                    <div id="results" class="text-gray-500">
                        Kliki nuppu, et kontrollida QR koodide turvalisust...
                    </div>
                </div>
            </div>

            <!-- Machine Details -->
            <div class="bg-white shadow rounded-lg mt-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Masinate detailid</h2>
                </div>
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200" id="machines-table" style="display: none;">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Masina number</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nimi</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">QR Secret</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">QR kood</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Turvaline URL</th>
                                </tr>
                            </thead>
                            <tbody id="machines-tbody" class="bg-white divide-y divide-gray-200">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        
        // Update status display
        function updateStatus(data) {
            document.getElementById('total-machines').textContent = data.total || 0;
            document.getElementById('secure-machines').textContent = data.secure || 0;
            document.getElementById('insecure-machines').textContent = data.insecure || 0;
        }

        // Display results
        function displayResults(message, type = 'info') {
            const results = document.getElementById('results');
            const colors = {
                'success': 'text-green-800 bg-green-100',
                'error': 'text-red-800 bg-red-100', 
                'warning': 'text-yellow-800 bg-yellow-100',
                'info': 'text-blue-800 bg-blue-100'
            };
            
            results.innerHTML = `
                <div class="p-4 rounded-lg ${colors[type] || colors.info}">
                    <pre class="whitespace-pre-wrap font-mono text-sm">${message}</pre>
                </div>
            `;
        }

        // Display machines table
        function displayMachines(machines) {
            const table = document.getElementById('machines-table');
            const tbody = document.getElementById('machines-tbody');
            
            if (!machines || machines.length === 0) {
                table.style.display = 'none';
                return;
            }

            tbody.innerHTML = machines.map(machine => {
                const hasSecret = machine.qr_secret && machine.qr_secret.length > 0;
                const hasQRCode = machine.qr_code_data;
                const baseUrl = window.location.origin;
                
                return `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${machine.machine_number}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${machine.name || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            ${hasSecret 
                                ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                     <i class="fas fa-check mr-1"></i> Olemas
                                   </span>`
                                : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                     <i class="fas fa-times mr-1"></i> Puudub
                                   </span>`
                            }
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            ${hasQRCode 
                                ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                     <i class="fas fa-check mr-1"></i> Olemas
                                   </span>`
                                : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                     <i class="fas fa-times mr-1"></i> Puudub
                                   </span>`
                            }
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            ${hasSecret 
                                ? `<code class="text-xs bg-gray-100 px-2 py-1 rounded">${baseUrl}/secure/${machine.machine_number}/${machine.qr_secret}</code>`
                                : '<span class="text-gray-400">-</span>'
                            }
                        </td>
                    </tr>
                `;
            }).join('');
            
            table.style.display = 'table';
        }

        // API calls
        async function checkStatus() {
            try {
                displayResults('Kontrollin masinate olukorda...', 'info');
                
                const response = await fetch(`${API_BASE}/machines`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'API viga');
                }

                const machines = data.machines || [];
                const total = machines.length;
                const secure = machines.filter(m => m.qr_secret && m.qr_secret.length > 0 && m.qr_code_data).length;
                const insecure = total - secure;

                updateStatus({ total, secure, insecure });
                displayMachines(machines);
                
                displayResults(`Leitud ${total} masinat.\n${secure} masinat on turvaliselt seadistatud.\n${insecure} masinat vajab parandamist.`, 
                              insecure > 0 ? 'warning' : 'success');
                
            } catch (error) {
                displayResults(`Viga staatuse kontrollimisel: ${error.message}`, 'error');
            }
        }

        async function ensureSecrets() {
            try {
                displayResults('Genereerin puuduvaid QR secret-e...', 'info');
                
                const response = await fetch(`${API_BASE}/machines/ensure-secrets`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'API viga');
                }

                displayResults(`${data.message}\n\nTulemused:\n${JSON.stringify(data.results, null, 2)}`, 'success');
                
                // Refresh status
                setTimeout(checkStatus, 1000);
                
            } catch (error) {
                displayResults(`Viga secret-ide genereerimisel: ${error.message}`, 'error');
            }
        }

        async function regenerateQRCodes() {
            try {
                displayResults('Regenereerin kõiki QR koode...', 'info');
                
                const response = await fetch(`${API_BASE}/machines/regenerate-all-qr`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'API viga');
                }

                displayResults(`${data.message}\n\nTulemused:\n${JSON.stringify(data.results, null, 2)}`, 'success');
                
                // Refresh status
                setTimeout(checkStatus, 1000);
                
            } catch (error) {
                displayResults(`Viga QR koodide regenereerimisel: ${error.message}`, 'error');
            }
        }

        async function testQRUrls() {
            try {
                displayResults('Testin QR URL-e...', 'info');
                
                const response = await fetch(`${API_BASE}/machines`);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'API viga');
                }

                const machines = data.machines || [];
                const baseUrl = window.location.origin;
                
                let testResults = 'QR URL-ide test:\n\n';
                
                machines.slice(0, 5).forEach(machine => {
                    if (machine.qr_secret) {
                        const secureUrl = `${baseUrl}/secure/${machine.machine_number}/${machine.qr_secret}`;
                        testResults += `${machine.machine_number}:\n`;
                        testResults += `  URL: ${secureUrl}\n`;
                        testResults += `  Staatus: ✅ Turvaline\n\n`;
                    } else {
                        testResults += `${machine.machine_number}:\n`;
                        testResults += `  URL: Puudub QR secret\n`;
                        testResults += `  Staatus: ❌ Ebaturvaline\n\n`;
                    }
                });
                
                displayResults(testResults, 'info');
                
            } catch (error) {
                displayResults(`Viga URL-ide testimisel: ${error.message}`, 'error');
            }
        }

        // Event listeners
        document.getElementById('check-status-btn').addEventListener('click', checkStatus);
        document.getElementById('ensure-secrets-btn').addEventListener('click', ensureSecrets);
        document.getElementById('regenerate-qr-btn').addEventListener('click', regenerateQRCodes);
        document.getElementById('test-qr-btn').addEventListener('click', testQRUrls);

        // Load initial status
        checkStatus();
    </script>
</body>
</html>
