import { pool } from '../src/config/database.js';

async function addSampleAnalyticsData() {
  console.log('🔄 Adding sample analytics data...');

  try {
    // Get existing machines
    const [machines] = await pool.execute('SELECT id, machine_number, name FROM machines LIMIT 3');
    
    if (machines.length === 0) {
      console.log('❌ No machines found. Please add machines first.');
      return;
    }

    console.log(`📊 Found ${machines.length} machines`);

    // Add sample maintenance requests with costs
    const maintenanceData = [
      {
        machine_id: machines[0].id,
        maintenance_type: 'preventive',
        title: '<PERSON><PERSON><PERSON><PERSON> hooldus',
        description: '<PERSON><PERSON><PERSON><PERSON> hooldus - <PERSON><PERSON>he<PERSON>',
        status: 'completed',
        cost: 150.50,
        scheduled_date: '2024-01-15',
        completed_date: '2024-01-15'
      },
      {
        machine_id: machines[0].id,
        maintenance_type: 'corrective',
        title: 'Rih<PERSON> vahetus',
        description: 'Rih<PERSON> vahetus',
        status: 'completed',
        cost: 85.00,
        scheduled_date: '2024-01-20',
        completed_date: '2024-01-21'
      },
      {
        machine_id: machines[1]?.id || machines[0].id,
        maintenance_type: 'preventive',
        title: 'Filtrite vahetus',
        description: 'Filtrite vahetus',
        status: 'completed',
        cost: 120.75,
        scheduled_date: '2024-01-10',
        completed_date: '2024-01-10'
      },
      {
        machine_id: machines[1]?.id || machines[0].id,
        maintenance_type: 'preventive',
        title: 'Määrimine ja puhastus',
        description: 'Määrimine ja puhastus',
        status: 'completed',
        cost: 95.25,
        scheduled_date: '2024-02-01',
        completed_date: '2024-02-01'
      },
      {
        machine_id: machines[2]?.id || machines[0].id,
        maintenance_type: 'corrective',
        title: 'Laagri vahetus',
        description: 'Laagri vahetus',
        status: 'completed',
        cost: 275.00,
        scheduled_date: '2024-01-25',
        completed_date: '2024-01-26'
      }
    ];

    for (const maintenance of maintenanceData) {
      await pool.execute(
        `INSERT INTO maintenance_requests
         (machine_id, maintenance_type, title, description, status, cost, scheduled_date, completed_date, operator_number, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'OP-001', NOW())`,
        [
          maintenance.machine_id,
          maintenance.maintenance_type,
          maintenance.title,
          maintenance.description,
          maintenance.status,
          maintenance.cost,
          maintenance.scheduled_date,
          maintenance.completed_date
        ]
      );
    }

    console.log(`✅ Added ${maintenanceData.length} maintenance records`);

    // Add sample issues (without costs - issues table doesn't have cost column)
    const issueData = [
      {
        machine_id: machines[0].id,
        issue_type: 'mechanical',
        title: 'Vibratsioon töö ajal',
        description: 'Vibratsioon töö ajal',
        status: 'resolved',
        reported_at: '2024-01-18',
        resolved_at: '2024-01-19'
      },
      {
        machine_id: machines[0].id,
        issue_type: 'electrical',
        title: 'Mootor ei käivitu',
        description: 'Mootor ei käivitu',
        status: 'resolved',
        reported_at: '2024-02-05',
        resolved_at: '2024-02-06'
      },
      {
        machine_id: machines[1]?.id || machines[0].id,
        issue_type: 'mechanical',
        title: 'Leke hüdraulikasüsteemis',
        description: 'Leke hüdraulikasüsteemis',
        status: 'resolved',
        reported_at: '2024-01-22',
        resolved_at: '2024-01-24'
      },
      {
        machine_id: machines[1]?.id || machines[0].id,
        issue_type: 'software',
        title: 'Programmi viga',
        description: 'Programmi viga',
        status: 'resolved',
        reported_at: '2024-02-10',
        resolved_at: '2024-02-10'
      },
      {
        machine_id: machines[2]?.id || machines[0].id,
        issue_type: 'mechanical',
        title: 'Ebatavaline müra',
        description: 'Ebatavaline müra',
        status: 'resolved',
        reported_at: '2024-01-30',
        resolved_at: '2024-01-31'
      }
    ];

    for (const issue of issueData) {
      await pool.execute(
        `INSERT INTO issues
         (machine_id, issue_type, title, description, status, reported_at, resolved_at, operator_number, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, 'OP-001', NOW())`,
        [
          issue.machine_id,
          issue.issue_type,
          issue.title,
          issue.description,
          issue.status,
          issue.reported_at,
          issue.resolved_at
        ]
      );
    }

    console.log(`✅ Added ${issueData.length} issue records`);

    // Add some older data for better analytics
    const olderMaintenanceData = [
      {
        machine_id: machines[0].id,
        maintenance_type: 'preventive',
        title: 'Kvartaalne hooldus',
        description: 'Kvartaalne hooldus',
        status: 'completed',
        cost: 200.00,
        scheduled_date: '2023-12-15',
        completed_date: '2023-12-15'
      },
      {
        machine_id: machines[1]?.id || machines[0].id,
        maintenance_type: 'preventive',
        title: 'Aasta lõpu hooldus',
        description: 'Aasta lõpu hooldus',
        status: 'completed',
        cost: 350.00,
        scheduled_date: '2023-12-20',
        completed_date: '2023-12-21'
      }
    ];

    for (const maintenance of olderMaintenanceData) {
      await pool.execute(
        `INSERT INTO maintenance_requests
         (machine_id, maintenance_type, title, description, status, cost, scheduled_date, completed_date, operator_number, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'OP-001', ?)`,
        [
          maintenance.machine_id,
          maintenance.maintenance_type,
          maintenance.title,
          maintenance.description,
          maintenance.status,
          maintenance.cost,
          maintenance.scheduled_date,
          maintenance.completed_date,
          maintenance.scheduled_date
        ]
      );
    }

    console.log(`✅ Added ${olderMaintenanceData.length} older maintenance records`);

    // Calculate totals
    const [maintenanceTotal] = await pool.execute(
      'SELECT SUM(cost) as total FROM maintenance_requests WHERE status = "completed"'
    );

    const [issueCount] = await pool.execute(
      'SELECT COUNT(*) as count FROM issues WHERE status = "resolved"'
    );

    console.log('\n📊 Analytics Data Summary:');
    console.log(`💰 Total Maintenance Costs: €${maintenanceTotal[0].total || 0}`);
    console.log(`🔧 Total Issues Resolved: ${issueCount[0].count || 0} (no cost tracking in issues table)`);
    console.log(`📈 Total Maintenance Costs: €${maintenanceTotal[0].total || 0}`);
    
    console.log('\n✅ Sample analytics data added successfully!');
    console.log('🔄 Refresh the reports page to see the new data.');

  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
addSampleAnalyticsData();
