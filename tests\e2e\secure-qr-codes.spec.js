import { test, expect } from '@playwright/test';

test.describe('US-027: Turvaline statsionaarne QR-kood', () => {
  let testMachineNumber;
  let testMachineSecret;

  test.beforeEach(async ({ page }) => {
    // Create test machine with secret via API
    testMachineNumber = 'E2E-QR-' + Date.now();
    
    // Login as admin to create machine
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');
    
    // Create machine
    await page.goto('/admin/machines/new');
    await page.fill('[name="machine_number"]', testMachineNumber);
    await page.fill('[name="name"]', 'E2E QR Test Machine');
    await page.fill('[name="manufacturer"]', 'Test Corp');
    await page.click('[type="submit"]');
    
    // Get the machine secret (would be generated automatically)
    // For now, we'll simulate this
    testMachineSecret = 'e2e_test_secret_123456789012345';
  });

  test.afterEach(async ({ page }) => {
    // Cleanup: delete test machine
    await page.goto('/admin/machines');
    await page.click(`[data-machine="${testMachineNumber}"] .delete-btn`);
    await page.click('.confirm-delete');
  });

  test('should use secure stationary QR codes with machine secrets', async ({ page }) => {
    // Valid QR code with correct machine secret should work
    await page.goto(`/secure/${testMachineNumber}/${testMachineSecret}`);
    await expect(page).toHaveURL(`/operator/${testMachineNumber}`);
    await expect(page.locator('[data-testid="machine-info"]')).toContainText(testMachineNumber);

    // Invalid secret should show 404 error
    await page.goto(`/secure/${testMachineNumber}/wrongsecret123456789012345`);
    await expect(page.locator('.alert-error')).toContainText('QR-kood ei ole kehtiv');
    await expect(page.locator('h1')).toContainText('404');

    // Different machine with wrong secret should fail
    await page.goto('/secure/NONEXISTENT/any_secret_123456789012345');
    await expect(page.locator('.alert-error')).toContainText('QR-kood ei ole kehtiv');
  });

  test('should enforce rate limiting on QR code access', async ({ page }) => {
    // Make multiple requests quickly to trigger rate limiting
    // Note: This test might be flaky due to timing, but demonstrates the concept
    
    const promises = [];
    for (let i = 0; i < 12; i++) {
      promises.push(
        page.goto(`/secure/${testMachineNumber}/${testMachineSecret}`, { waitUntil: 'networkidle' })
      );
    }
    
    await Promise.all(promises);
    
    // The last few requests should be rate limited
    await page.goto(`/secure/${testMachineNumber}/${testMachineSecret}`);
    
    // Check if we get rate limited (429 status or rate limit message)
    const content = await page.content();
    const isRateLimited = content.includes('Liiga palju päringuid') || 
                         content.includes('Rate limit exceeded') ||
                         page.url().includes('429');
    
    if (isRateLimited) {
      expect(content).toContain('Liiga palju päringuid');
    }
  });

  test('should log QR code access attempts', async ({ page }) => {
    // Valid access should be logged
    await page.goto(`/secure/${testMachineNumber}/${testMachineSecret}`);

    // Invalid access should be logged as suspicious
    await page.goto(`/secure/${testMachineNumber}/hacker_attempt_123456789012`);

    // Admin should see access logs
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    await page.goto('/admin/security/qr-logs');
    
    // Check that logs contain our test machine
    await expect(page.locator('tbody')).toContainText(testMachineNumber);
    
    // Should show both valid and invalid attempts
    const tableContent = await page.locator('tbody').textContent();
    expect(tableContent).toContain('Valid access');
    expect(tableContent).toContain('Invalid secret');
  });

  test('should block external network access', async ({ page, context }) => {
    // This test simulates external access by setting headers
    // In real scenario, this would be handled by network infrastructure
    
    await page.route('**/*', (route, request) => {
      // Simulate external IP
      route.continue({
        headers: {
          ...request.headers(),
          'X-Forwarded-For': '*******', // External IP
          'X-Real-IP': '*******'
        }
      });
    });

    await page.goto(`/secure/${testMachineNumber}/${testMachineSecret}`);
    
    // Should be blocked with 403 Forbidden
    await expect(page.locator('h1')).toContainText('403');
    await expect(page.locator('.alert-error')).toContainText('Juurdepääs keelatud');
  });

  test('should generate and regenerate QR secrets for machines', async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    // Go to machine details
    await page.goto('/admin/machines');
    await page.click(`[data-machine="${testMachineNumber}"] .view-btn`);

    // Should show QR code section with security info
    await expect(page.locator('.qr-security-section')).toBeVisible();
    await expect(page.locator('.qr-secret-display')).toContainText('QR Turvakood:');

    // Regenerate QR secret
    await page.click('.regenerate-qr-secret-btn');
    await page.click('.confirm-regenerate');

    // Should show success message
    await expect(page.locator('.alert-success')).toContainText('QR turvakood uuendatud');

    // Secret should be different (we can't easily test this in E2E, but the UI should reflect it)
    await expect(page.locator('.qr-secret-display')).toBeVisible();
  });

  test('should display QR code with secure URL', async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    // Go to machine details
    await page.goto('/admin/machines');
    await page.click(`[data-machine="${testMachineNumber}"] .view-btn`);

    // QR code should contain secure URL format
    const qrImage = page.locator('.qr-code-display img');
    await expect(qrImage).toBeVisible();

    // Check that the QR code URL format is secure (contains /secure/ path)
    // This would require QR code reading capability, so we check the display text instead
    await expect(page.locator('.qr-url-display')).toContainText('/secure/');
    await expect(page.locator('.qr-url-display')).toContainText(testMachineNumber);
  });

  test('should show security warnings for QR codes', async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');

    // Go to security logs
    await page.goto('/admin/security/qr-logs');

    // Should show security warnings section
    await expect(page.locator('.security-warnings')).toBeVisible();
    await expect(page.locator('.security-info')).toContainText('QR-koodide turvalisus');

    // Should show rate limiting info
    await expect(page.locator('.rate-limit-info')).toContainText('10 kasutamist 15 minuti jooksul');

    // Should show network access info
    await expect(page.locator('.network-access-info')).toContainText('Ainult sisevõrk');
  });
});
