// Unified navigation template for all pages
export function generateNavigation(currentPage = '', pageTitle = 'CMMS') {
  const navItems = [
    { href: '/admin', icon: 'fas fa-home', label: 'Dashboard', key: 'dashboard' },
    { href: '/machines', icon: 'fas fa-cogs', label: 'Masinad', key: 'machines' },
    { href: '/admin/machine-groups', icon: 'fas fa-layer-group', label: 'Grupid', key: 'groups' },
    { href: '/admin/reports', icon: 'fas fa-chart-bar', label: 'Aruanded', key: 'reports' },
    { href: '/admin/issues', icon: 'fas fa-exclamation-triangle', label: 'Rikked', key: 'issues' },
    { href: '/admin/maintenance', icon: 'fas fa-wrench', label: 'Hooldus', key: 'maintenance' },
    { href: '/admin/partners', icon: 'fas fa-handshake', label: 'Partnerid', key: 'partners' },
    { href: '/admin/security/qr-logs', icon: 'fas fa-shield-alt', label: 'QR Turvalisus', key: 'security' },
  ];

  // Generate desktop navigation
  const desktopNav = navItems
    .map(item => {
      const isActive = item.key === currentPage;
      const activeClass = isActive ? 'bg-blue-800' : 'hover:bg-blue-700';

      return `
      <a href="${item.href}" class="${activeClass} px-3 py-2 rounded min-h-[44px] flex items-center">
        <i class="${item.icon} mr-2"></i>${item.label}
      </a>
    `;
    })
    .join('');

  // Generate mobile navigation
  const mobileNav = navItems
    .map(item => {
      const isActive = item.key === currentPage;
      const activeClass = isActive ? 'bg-blue-800' : 'hover:bg-blue-700';

      return `
      <a href="${item.href}" class="block ${activeClass} px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center">
        <i class="${item.icon} mr-2"></i>${item.label}
      </a>
    `;
    })
    .join('');

  return `
    <!-- Navigation -->
    <nav class="bg-blue-600 text-white">
      <div class="container mx-auto px-4">
        <!-- Desktop Navigation -->
        <div class="hidden md:flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold">${pageTitle}</h1>
          </div>
          <div class="flex space-x-4">
            ${desktopNav}
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="md:hidden">
          <div class="flex justify-between items-center py-3">
            <h1 class="text-lg font-bold">${pageTitle}</h1>
            <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
              <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
            </button>
          </div>
          
          <!-- Mobile Menu -->
          <div id="mobile-menu" class="hidden pb-4">
            <div class="space-y-2">
              ${mobileNav}
            </div>
          </div>
        </div>
      </div>
    </nav>
  `;
}

// Mobile menu JavaScript
export const mobileMenuScript = `
  // Mobile menu toggle
  function toggleMobileMenu() {
    const menu = document.getElementById('mobile-menu');
    const icon = document.getElementById('mobile-menu-icon');
    
    if (menu.classList.contains('hidden')) {
      menu.classList.remove('hidden');
      icon.classList.remove('fa-bars');
      icon.classList.add('fa-times');
    } else {
      menu.classList.add('hidden');
      icon.classList.remove('fa-times');
      icon.classList.add('fa-bars');
    }
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    const menu = document.getElementById('mobile-menu');
    const button = event.target.closest('button[onclick="toggleMobileMenu()"]');
    
    if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
      toggleMobileMenu();
    }
  });
`;
