import User from './User.js';
import Session from './Session.js';
import LoginAttempt from './LoginAttempt.js';
import { Machine } from './Machine.js';
import { Issue } from './Issue.js';
import { MaintenanceRequest } from './MaintenanceRequest.js';
import Part from './Part.js';
import Project from './Project.js';
import Document from './Document.js';
import { MachineGroup } from './MachineGroup.js';

// Define associations
User.hasMany(Session, { foreignKey: 'user_id', as: 'sessions' });
Session.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export {
  User,
  Session,
  LoginAttempt,
  Machine,
  Issue,
  MaintenanceRequest as Maintenance,
  Part,
  Project,
  Document,
  MachineGroup
};
