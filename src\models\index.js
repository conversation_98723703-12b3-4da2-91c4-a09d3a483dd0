// Import all models using mysql2/promise
import { User } from './User.js';
import { Session } from './Session.js';
import { LoginAttempt } from './LoginAttempt.js';
import { Machine } from './Machine.js';
import { Issue } from './Issue.js';
import { MaintenanceRequest } from './MaintenanceRequest.js';
import { Notification } from './Notification.js';
import Part from './Part.js';
import Project from './Project.js';
import Document from './Document.js';
import { MachineGroup } from './MachineGroup.js';

export {
  User,
  Session,
  LoginAttempt,
  Machine,
  Issue,
  MaintenanceRequest as Maintenance,
  Notification,
  Part,
  Project,
  Document,
  MachineGroup
};
