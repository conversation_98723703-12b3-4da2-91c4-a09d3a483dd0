import { pool } from '../config/database.js';

class Document {
  constructor(data) {
    this.id = data.id;
    this.machine_id = data.machine_id;
    this.project_id = data.project_id;
    this.title = data.title;
    this.description = data.description;
    this.document_type = data.document_type;
    this.file_name = data.file_name;
    this.file_data = data.file_data;
    this.file_size = data.file_size;
    this.mime_type = data.mime_type;
    this.file_extension = data.file_extension;
    this.uploaded_by = data.uploaded_by;
    this.version = data.version;
    this.is_active = data.is_active;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Kõigi dokumentide leidmine
  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT d.*,
               m.name as machine_name, m.machine_number,
               p.title as project_title
        FROM documents d
        LEFT JOIN machines m ON d.machine_id = m.id
        LEFT JOIN development_projects p ON d.project_id = p.id
        WHERE d.is_active = TRUE
      `;
      const params = [];

      // Filtreerimine masina järgi
      if (filters.machine_id) {
        query += ' AND d.machine_id = ?';
        params.push(filters.machine_id);
      }

      // Filtreerimine projekti järgi
      if (filters.project_id) {
        query += ' AND d.project_id = ?';
        params.push(filters.project_id);
      }

      // Filtreerimine dokumendi tüübi järgi
      if (filters.document_type) {
        query += ' AND d.document_type = ?';
        params.push(filters.document_type);
      }

      // Otsing
      if (filters.search) {
        query += ' AND (d.title LIKE ? OR d.description LIKE ? OR d.file_name LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // Sorteerimine
      const sortBy = filters.sort_by || 'created_at';
      const sortOrder = filters.sort_order || 'DESC';
      query += ` ORDER BY d.${sortBy} ${sortOrder}`;

      // Limit
      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(parseInt(filters.limit));
      }

      const [rows] = await pool.execute(query, params);
      return rows.map(row => new Document(row));
    } catch (error) {
      console.error('Error finding documents:', error);
      throw error;
    }
  }

  // Ühe dokumendi leidmine ID järgi
  static async findById(id) {
    try {
      const query = `
        SELECT d.*, m.name as machine_name, m.machine_number
        FROM documents d
        LEFT JOIN machines m ON d.machine_id = m.id
        WHERE d.id = ? AND d.is_active = TRUE
      `;
      const [rows] = await pool.execute(query, [id]);
      return rows.length > 0 ? new Document(rows[0]) : null;
    } catch (error) {
      console.error('Error finding document by ID:', error);
      throw error;
    }
  }

  // Masina dokumentide leidmine
  static async findByMachine(machineId) {
    try {
      const query = `
        SELECT d.*, m.name as machine_name, m.machine_number
        FROM documents d
        LEFT JOIN machines m ON d.machine_id = m.id
        WHERE d.machine_id = ? AND d.is_active = TRUE
        ORDER BY d.document_type, d.created_at DESC
      `;
      const [rows] = await pool.execute(query, [machineId]);
      return rows.map(row => new Document(row));
    } catch (error) {
      console.error('Error finding documents by machine:', error);
      throw error;
    }
  }

  // Operaatori jaoks nähtavate dokumentide leidmine
  static async findOperatorDocuments(machineId) {
    try {
      const query = `
        SELECT d.*, m.name as machine_name, m.machine_number
        FROM documents d
        LEFT JOIN machines m ON d.machine_id = m.id
        WHERE d.machine_id = ? AND d.is_active = TRUE AND d.operator_visible = TRUE
        ORDER BY 
          CASE d.category 
            WHEN 'safety' THEN 1
            WHEN 'operation' THEN 2
            WHEN 'maintenance' THEN 3
            WHEN 'technical' THEN 4
            ELSE 5
          END,
          d.document_type, d.created_at DESC
      `;
      const [rows] = await pool.execute(query, [machineId]);
      return rows.map(row => new Document(row));
    } catch (error) {
      console.error('Error finding operator documents:', error);
      // Fallback to basic documents if operator_visible column doesn't exist yet
      try {
        const fallbackQuery = `
          SELECT d.*, m.name as machine_name, m.machine_number
          FROM documents d
          LEFT JOIN machines m ON d.machine_id = m.id
          WHERE d.machine_id = ? AND d.is_active = TRUE 
          AND d.document_type IN ('manual', 'safety_guide', 'operating_procedure')
          ORDER BY d.document_type, d.created_at DESC
        `;
        const [fallbackRows] = await pool.execute(fallbackQuery, [machineId]);
        return fallbackRows.map(row => new Document(row));
      } catch (fallbackError) {
        console.error('Error with fallback query:', fallbackError);
        return [];
      }
    }
  }

  // Projekti dokumentide leidmine
  static async findByProject(projectId) {
    try {
      const query = `
        SELECT d.*, p.title as project_title
        FROM documents d
        LEFT JOIN development_projects p ON d.project_id = p.id
        WHERE d.project_id = ? AND d.is_active = TRUE
        ORDER BY d.created_at DESC
      `;
      const [rows] = await pool.execute(query, [projectId]);
      return rows.map(row => new Document(row));
    } catch (error) {
      console.error('Error finding documents by project:', error);
      throw error;
    }
  }

  // Uue dokumendi loomine
  static async create(documentData) {
    try {
      const query = `
        INSERT INTO documents (
          machine_id, project_id, title, description, document_type, file_name,
          file_data, file_size, mime_type, file_extension, uploaded_by, version
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        documentData.machine_id || null,
        documentData.project_id || null,
        documentData.title,
        documentData.description || null,
        documentData.document_type,
        documentData.file_name,
        documentData.file_data,
        documentData.file_size,
        documentData.mime_type,
        documentData.file_extension,
        documentData.uploaded_by || null,
        documentData.version || '1.0',
      ];

      const [result] = await pool.execute(query, params);
      return await Document.findById(result.insertId);
    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    }
  }

  // Dokumendi uuendamine
  static async update(id, updateData) {
    try {
      const fields = [];
      const params = [];

      // Uuendatavad väljad
      const allowedFields = ['title', 'description', 'document_type', 'version'];

      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          fields.push(`${field} = ?`);
          params.push(updateData[field]);
        }
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      params.push(id);
      const query = `UPDATE documents SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;

      await pool.execute(query, params);
      return await Document.findById(id);
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  }

  // Dokumendi kustutamine (soft delete)
  static async delete(id) {
    try {
      const query =
        'UPDATE documents SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
      await pool.execute(query, [id]);
      return true;
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  // Dokumendi faili andmete saamine
  static async getFileData(id) {
    try {
      const query = `
        SELECT file_data, file_name, mime_type, file_size
        FROM documents
        WHERE id = ? AND is_active = TRUE
      `;
      const [rows] = await pool.execute(query, [id]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Error getting file data:', error);
      throw error;
    }
  }

  // Dokumentide statistika
  static async getStatistics() {
    try {
      const query = `
        SELECT
          COUNT(*) as total_documents,
          COUNT(CASE WHEN document_type = 'manual' THEN 1 END) as manuals_count,
          COUNT(CASE WHEN document_type = 'schematic' THEN 1 END) as schematics_count,
          COUNT(CASE WHEN document_type = 'photo' THEN 1 END) as photos_count,
          COUNT(CASE WHEN document_type = 'certificate' THEN 1 END) as certificates_count,
          SUM(file_size) as total_storage_bytes,
          AVG(file_size) as average_file_size
        FROM documents
        WHERE is_active = TRUE
      `;
      const [rows] = await pool.execute(query);
      return rows[0];
    } catch (error) {
      console.error('Error getting documents statistics:', error);
      throw error;
    }
  }

  // Dokumendi ligipääsu logimine
  static async logAccess(documentId, action, userInfo = {}) {
    try {
      const query = `
        INSERT INTO document_access_log (
          document_id, user_identifier, action, user_agent, ip_address
        ) VALUES (?, ?, ?, ?, ?)
      `;

      const params = [
        documentId,
        userInfo.user_identifier || 'anonymous',
        action,
        userInfo.user_agent || null,
        userInfo.ip_address || null,
      ];

      await pool.execute(query, params);
    } catch (error) {
      console.error('Error logging document access:', error);
      // Don't throw error for logging failures
    }
  }

  // Dokumendi tüüpide nimekiri
  static getDocumentTypes() {
    return [
      { value: 'manual', label: 'Kasutusjuhend' },
      { value: 'schematic', label: 'Skeem/Joonis' },
      { value: 'photo', label: 'Foto' },
      { value: 'certificate', label: 'Sertifikaat' },
      { value: 'maintenance_log', label: 'Hoolduse logi' },
      { value: 'other', label: 'Muu' },
    ];
  }

  // Lubatud failitüübid
  static getAllowedMimeTypes() {
    return [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
  }

  // Maksimaalse faili suuruse kontroll (10MB)
  static getMaxFileSize() {
    return 10 * 1024 * 1024; // 10MB in bytes
  }

  // Operaatori nähtavuse muutmine
  static async updateOperatorVisibility(id, operatorVisible) {
    try {
      const [result] = await pool.execute(
        'UPDATE documents SET operator_visible = ? WHERE id = ? AND is_active = TRUE',
        [operatorVisible, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error updating operator visibility:', error);
      throw error;
    }
  }
}

export default Document;
