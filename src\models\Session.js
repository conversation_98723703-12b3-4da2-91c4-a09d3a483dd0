import { pool } from '../config/database.js';
import { User } from './User.js';
import crypto from 'crypto';

export class Session {
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.ip_address = data.ip_address;
    this.user_agent = data.user_agent;
    this.expires_at = data.expires_at;
    this.created_at = data.created_at;
  }

  isExpired() {
    return new Date() >= new Date(this.expires_at);
  }

  async extend() {
    try {
      const newExpiresAt = new Date(Date.now() + 8 * 60 * 60 * 1000); // Extend by 8 hours
      await pool.execute(
        'UPDATE sessions SET expires_at = ? WHERE id = ?',
        [newExpiresAt, this.id]
      );
      this.expires_at = newExpiresAt;
      return true;
    } catch (error) {
      console.error('Error extending session:', error);
      return false;
    }
  }

  static async createSession(userId, ipAddress, userAgent) {
    try {
      const sessionId = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + 8 * 60 * 60 * 1000); // 8 hours

      await pool.execute(
        `INSERT INTO sessions (id, user_id, ip_address, user_agent, expires_at) 
         VALUES (?, ?, ?, ?, ?)`,
        [sessionId, userId, ipAddress, userAgent, expiresAt]
      );

      return await Session.findById(sessionId);
    } catch (error) {
      console.error('Error creating session:', error);
      throw error;
    }
  }

  static async findById(sessionId) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM sessions WHERE id = ? AND expires_at > NOW()',
        [sessionId]
      );
      return rows.length > 0 ? new Session(rows[0]) : null;
    } catch (error) {
      console.error('Error finding session:', error);
      return null;
    }
  }

  static async findByIdWithUser(sessionId) {
    try {
      const [rows] = await pool.execute(
        `SELECT s.*, u.id as user_id, u.username, u.email, u.full_name, u.role, u.is_active
         FROM sessions s
         JOIN users u ON s.user_id = u.id
         WHERE s.id = ? AND s.expires_at > NOW() AND u.is_active = 1`,
        [sessionId]
      );
      
      if (rows.length === 0) {
        return null;
      }
      
      const sessionData = rows[0];
      const session = new Session(sessionData);
      
      // Create User instance with proper methods
      session.user = new User({
        id: sessionData.user_id,
        username: sessionData.username,
        email: sessionData.email,
        full_name: sessionData.full_name,
        role: sessionData.role,
        is_active: sessionData.is_active
      });
      
      return session;
    } catch (error) {
      console.error('Error finding session with user:', error);
      return null;
    }
  }

  static async destroy(sessionId) {
    try {
      await pool.execute('DELETE FROM sessions WHERE id = ?', [sessionId]);
      return true;
    } catch (error) {
      console.error('Error destroying session:', error);
      return false;
    }
  }

  static async cleanup() {
    try {
      await pool.execute('DELETE FROM sessions WHERE expires_at <= NOW()');
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
    }
  }
}
