# CMMS - Computerized Maintenance Management System

A modern, fullstack CMMS application built with Bun, Hono, and MariaDB. Designed to manage up to 30 machines, their maintenance, documentation, and spare parts.

## 🚀 Features

### Core Management
- **Machine Management**: Register and manage machines with QR codes and grouping
- **Mobile-Friendly**: QR code scanning for operators on mobile devices
- **Issue Reporting**: Report and track machine issues with photo support
- **Maintenance Planning**: Schedule and track maintenance activities with materials tracking
- **Spare Parts Management**: Inventory tracking with low-stock alerts and machine associations
- **Document Management**: Upload/download manuals and certificates stored in database
- **Statistics & Reports**: Machine efficiency and issue analytics with PDF/Excel export

### Security & User Management
- **Role-Based Access Control**: Admin, Maintenance, Operator, and Viewer roles with different permissions
- **User Authentication**: Secure login system with session management and failed attempt tracking
- **Password Management**: Password change functionality with strength validation
- **User Administration**: Admin interface for creating and managing user accounts
- **Profile Management**: User profile pages with account information and password change

### Advanced Features
- **Materials Tracking**: Track parts and materials used in maintenance and issue resolution
- **External Partners**: Manage external maintenance partners with notifications
- **Project Management**: Track development projects with document and photo support
- **QR Code Printing**: Professional QR code printing for machine labeling
- **Machine Groups**: Organize machines by type with custom colors and icons
- **Photo Support**: Camera integration for issue reporting and project documentation
- **Secure QR Codes**: Machine-specific secrets for secure stationary QR code access

## 🛠 Tech Stack

- **Runtime**: Bun
- **Backend**: Hono.js
- **Database**: MariaDB/MySQL
- **Frontend**: Server-side rendering (EJS) + Tailwind CSS + Alpine.js
- **Testing**: Playwright (E2E) + Vitest (API/Unit tests)
- **QR Codes**: qrcode library
- **File Storage**: Database LONGBLOB

## 📋 Prerequisites

- [Bun](https://bun.sh/) (latest version)
- [MariaDB](https://mariadb.org/) or MySQL
- Git

## 🔧 Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd CMMS
   ```

2. **Install dependencies**

   ```bash
   bun install
   ```

3. **Setup PATH environment variables (Windows)**

   ```bash
   # Add MariaDB to PATH
   setx PATH "%PATH%;C:\Program Files\MariaDB 11.7\bin"

   # Add Bun to PATH (if not already added)
   setx PATH "%PATH%;C:\Users\<USER>\.bun\bin"

   # Restart terminal after PATH changes
   ```

4. **Setup environment**

   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

5. **Setup database**

   ```bash
   # Start MariaDB service
   # Create database and run migrations
   bun run migrate
   ```

6. **Start development server**

   ```bash
   bun run dev
   ```

7. **Access the application**
   - Login Page: http://localhost:8080/login
   - Admin Dashboard: http://localhost:8080/admin
   - API Health Check: http://localhost:8080/health

   **Default Admin Credentials:**
   - Username: `admin`
   - Password: `admin123`

## 🚀 Quick Start Scripts

For easier development, use the provided batch scripts:

```bash
# Start MariaDB only
scripts/start-mariadb.bat

# Start CMMS server only
scripts/start-cmms.bat

# Start both MariaDB and CMMS server
scripts/start-all.bat
```

## 🧪 Testing

### Run API Tests

```bash
bun test
```

### Run E2E Tests

```bash
bun run test:e2e
```

### Run Tests in Watch Mode

```bash
bun run test:watch
```

## 📁 Project Structure

```
CMMS/
├── src/
│   ├── config/          # Database and app configuration
│   ├── models/          # Database models
│   ├── routes/          # API and page routes
│   ├── controllers/     # Business logic
│   ├── middleware/      # Custom middleware
│   ├── utils/           # Utility functions
│   └── views/           # EJS templates
├── tests/
│   ├── e2e/            # Playwright E2E tests
│   ├── unit/           # Vitest unit tests
│   └── fixtures/       # Test data
├── database/
│   ├── migrations/     # Database migration files (001-014)
│   ├── schema.sql      # Legacy database schema
│   ├── migrate.js      # Migration script with tracking
│   └── seed.js         # Seed data
├── public/             # Static assets
└── docs/               # Documentation
```

## 🔄 Development Workflow (TDD)

This project follows Test-Driven Development:

1. **🔴 RED**: Write failing test first
2. **🟢 GREEN**: Implement minimal code to pass
3. **🔵 REFACTOR**: Improve code while keeping tests green

## 📊 Database Schema

The application uses the following main tables:

### Core Tables
- `machines` - Machine registry with QR codes and group associations
- `machine_groups` - Machine categorization with colors and icons
- `issues` - Issue reports with photo support and partner assignments
- `maintenance_requests` - Maintenance planning and tracking
- `parts` - Spare parts inventory with machine associations
- `documents` - Machine documentation stored in database
- `projects` - Development projects with document and photo support
- `partners` - External maintenance partners

### Security Tables
- `users` - User accounts with roles and authentication data
- `sessions` - Active user sessions with expiration tracking
- `login_attempts` - Security logging for failed login attempts
- `migrations` - Database migration tracking

### Materials Tracking
- `maintenance_materials` - Materials used in maintenance activities
- `issue_materials` - Materials used in issue resolution
- `machine_parts` - Parts associated with specific machines

### Support Tables
- `notifications` - System notifications and alerts
- Various views for reporting and analytics

## 🔗 API Endpoints

### Machines
- `GET /api/machines` - List all machines
- `POST /api/machines` - Create new machine
- `GET /api/machines/:id` - Get machine details
- `PUT /api/machines/:id` - Update machine
- `DELETE /api/machines/:id` - Delete machine
- `POST /api/machines/:id/regenerate-qr` - Regenerate QR code

### Machine Groups
- `GET /api/machine-groups` - List all machine groups
- `POST /api/machine-groups` - Create new group
- `GET /api/machine-groups/:id` - Get group details
- `PUT /api/machine-groups/:id` - Update group
- `DELETE /api/machine-groups/:id` - Delete group
- `GET /api/machine-groups/colors` - Available colors
- `GET /api/machine-groups/icons` - Available icons

### Issues
- `GET /api/issues` - List all issues
- `POST /api/issues` - Create new issue
- `GET /api/issues/:id` - Get issue details
- `PUT /api/issues/:id` - Update issue
- `GET /api/issues/:id/photo` - Get issue photo
- `GET /api/issues/:id/materials` - Get issue materials
- `POST /api/issues/:id/materials` - Add material to issue

### Maintenance
- `GET /api/maintenance` - List maintenance requests
- `POST /api/maintenance` - Create maintenance request
- `GET /api/maintenance/:id` - Get maintenance details
- `PUT /api/maintenance/:id` - Update maintenance
- `GET /api/maintenance/:id/materials` - Get maintenance materials
- `POST /api/maintenance/:id/materials` - Add material to maintenance

### Parts
- `GET /api/parts` - List all parts
- `POST /api/parts` - Create new part
- `GET /api/parts/:id` - Get part details
- `PUT /api/parts/:id` - Update part
- `DELETE /api/parts/:id` - Delete part

### Partners
- `GET /api/partners` - List external partners
- `POST /api/partners` - Create new partner
- `GET /api/partners/:id` - Get partner details
- `PUT /api/partners/:id` - Update partner

### Documents
- `GET /api/documents` - List all documents
- `POST /api/documents` - Upload new document
- `GET /api/documents/:id/download` - Download document

### Projects
- `GET /api/projects` - List all projects
- `POST /api/projects` - Create new project
- `GET /api/projects/:id` - Get project details
- `PUT /api/projects/:id` - Update project

### Users & Authentication
- `POST /auth` - User login
- `GET /auth/logout` - User logout
- `GET /api/users` - List all users (admin only)
- `POST /api/users` - Create new user (admin only)
- `GET /api/users/:id` - Get user details (admin only)
- `PATCH /api/users/:id` - Update user (admin only)
- `PATCH /api/users/:id/toggle` - Toggle user status (admin only)
- `GET /api/profile` - Get current user profile
- `POST /api/profile/change-password` - Change user password

### Files
- `GET /api/files/qr/:machineId` - Download QR code

### Reports
- `GET /api/reports/groups` - Group analytics
- `GET /api/export/groups-pdf` - Export groups to PDF
- `GET /api/export/groups-excel` - Export groups to Excel

## 🌐 Web Routes

### Admin Routes
- `/` - Redirect to admin dashboard
- `/admin` - Admin dashboard with statistics
- `/admin/machines` - Machine management
- `/admin/machine-groups` - Machine groups management
- `/admin/issues` - Issues management
- `/admin/maintenance` - Maintenance management
- `/admin/parts` - Parts inventory management
- `/admin/partners` - External partners management
- `/admin/documents` - Document management
- `/admin/projects` - Project management
- `/admin/reports` - Reports and analytics

### Machine Routes
- `/machines` - Machine list (all roles)
- `/machines/new` - Add new machine form
- `/machines/:id` - Machine details with QR code
- `/machines/:id/edit` - Edit machine form

### Project Routes
- `/projects` - Project list (all roles)
- `/projects/new` - Add new project form
- `/projects/:id` - Project details
- `/projects/:id/edit` - Edit project form

### Operator Routes (Mobile-Friendly)
- `/operator/:machineNumber` - Operator dashboard
- `/operator/:machineNumber/issue` - Report issue form
- `/operator/:machineNumber/maintenance` - Request maintenance form

### Detail Views
- `/admin/issues/:id` - Issue detail view with materials
- `/admin/maintenance/:id` - Maintenance detail view with materials
- `/admin/projects/:id` - Project detail view

## 🚀 Deployment

### Production Setup

1. **Environment Configuration**

   ```bash
   NODE_ENV=production
   DB_PASSWORD=secure_password
   BASE_URL=https://your-domain.com
   ```

2. **Database Setup**

   - Create production database
   - Run migrations
   - Setup regular backups

3. **Security**
   - Use HTTPS
   - Set strong passwords
   - Configure firewall
   - Regular security updates

## 📝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Write tests for your changes
4. Implement the feature (following TDD)
5. Commit changes (`git commit -m 'Add amazing feature'`)
6. Push to branch (`git push origin feature/amazing-feature`)
7. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Check the documentation in `/docs`
- Review the test files for usage examples

## 🗺 Roadmap

- [x] **Phase 1**: Core machine management with QR codes
- [x] **Phase 2**: Issue reporting and operator interface
- [x] **Phase 3**: Maintenance planning and scheduling
- [x] **Phase 4**: Spare parts management with machine associations
- [x] **Phase 5**: Document management with database storage
- [x] **Phase 6**: Statistics and reporting with PDF/Excel export
- [x] **Phase 7**: Development projects tracking
- [x] **Phase 8**: Machine groups and categorization
- [x] **Phase 9**: External partners management
- [x] **Phase 10**: Materials tracking for maintenance and issues
- [x] **Phase 11**: QR code printing functionality
- [x] **Phase 12**: User authentication and security system
  - [x] US-018: Administrator login
  - [x] US-022: Password change functionality
  - [x] User management interface
  - [x] Role-based access control
  - [x] Session management
  - [x] Security logging
- [ ] **Phase 13**: Advanced reporting and analytics
- [ ] **Phase 14**: Mobile app development
- [ ] **Phase 15**: API integrations and webhooks
- [ ] **Phase 16**: Advanced notifications and alerts

## User Roles & Permissions

### 🛡️ Admin
- Full system access
- User management (create, edit, deactivate users)
- All machine, maintenance, and project operations
- System configuration and reports

### 🔧 Maintenance (Tehnik)
- All features except user management
- Machine maintenance and repairs
- Parts management and inventory
- Project management and documentation

### 👷 Operator (Meister)
- View machines and their details
- Report issues and maintenance needs
- Initiate projects and add documentation
- View-only access to parts and maintenance history

### 👁️ Viewer
- Read-only access to all information
- Cannot create, edit, or delete any data
- Suitable for management oversight

## Security Features

- **Password Security**: bcrypt hashing with salt rounds
- **Session Management**: Secure session handling with expiration
- **Failed Login Protection**: Automatic account locking after failed attempts
- **Role-Based Access**: Granular permissions based on user roles
- **Audit Logging**: Security event logging for compliance
- **QR Code Security**: Machine-specific secrets for secure access

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
