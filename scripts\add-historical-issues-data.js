import { pool } from '../src/config/database.js';

async function addHistoricalIssuesData() {
  console.log('🔄 Adding historical issues data for better analytics...');

  try {
    // Get first 10 machines for adding historical data
    const [machines] = await pool.execute('SELECT id, machine_number, name FROM machines LIMIT 10');
    
    if (machines.length === 0) {
      console.log('❌ No machines found. Please add machines first.');
      return;
    }

    console.log(`📊 Found ${machines.length} machines for historical data`);

    // Generate historical issues for better MTBF/MTTR calculations
    const historicalIssues = [];

    machines.forEach((machine, machineIndex) => {
      // Add 3-5 historical issues per machine over the last 12 months
      const issueCount = 3 + Math.floor(Math.random() * 3); // 3-5 issues per machine
      
      for (let i = 0; i < issueCount; i++) {
        // Generate random dates over the last 12 months (but not in future)
        const daysAgo = Math.floor(Math.random() * 365) + 7; // 7-365 days ago (avoid recent dates)
        const reportedDate = new Date();
        reportedDate.setDate(reportedDate.getDate() - daysAgo);
        reportedDate.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);

        // Generate resolution time (1-72 hours)
        const resolutionHours = Math.floor(Math.random() * 72) + 1;
        const resolvedDate = new Date(reportedDate.getTime() + (resolutionHours * 60 * 60 * 1000));

        const issueTypes = ['mechanical', 'electrical', 'software', 'safety', 'quality', 'other'];
        const issueType = issueTypes[Math.floor(Math.random() * issueTypes.length)];

        const issueTitles = {
          mechanical: [
            'Vibratsioon töö ajal',
            'Ebatavaline müra',
            'Laagri kulumine',
            'Rihma lõhkemine',
            'Võlli deformatsioon',
            'Leke hüdraulikasüsteemis',
            'Pumba rike'
          ],
          electrical: [
            'Mootor ei käivitu',
            'Lühis elektrisüsteemis',
            'Anduri rike',
            'Kaabli katkemine',
            'Kontrolleri viga'
          ],
          software: [
            'Programmi viga',
            'Süsteemi külmumine',
            'Andmete kaotus',
            'Kalibreerimine vajalik',
            'Tarkvara uuendus vajalik'
          ],
          safety: [
            'Ohutu seisak vajalik',
            'Kaitsevahendite rike',
            'Hädapeatamise probleem',
            'Ohtlik vibratsioon',
            'Kuumenemine'
          ],
          quality: [
            'Toote kvaliteedi langus',
            'Mõõtmete kõrvalekalle',
            'Pinna kvaliteedi probleem',
            'Materjali raiskamine',
            'Standardist kõrvalekalle'
          ],
          other: [
            'Õhuleke süsteemis',
            'Kompressori rike',
            'Ventiili rike',
            'Filtri ummistus',
            'Surve ebastabiilsus'
          ]
        };

        const titles = issueTitles[issueType];
        const title = titles[Math.floor(Math.random() * titles.length)];

        historicalIssues.push({
          machine_id: machine.id,
          machine_name: machine.name,
          machine_number: machine.machine_number,
          issue_type: issueType,
          title: title,
          description: `${title} - automaatselt genereeritud ajalooandmed`,
          status: 'resolved',
          reported_at: reportedDate.toISOString().slice(0, 19).replace('T', ' '),
          resolved_at: resolvedDate.toISOString().slice(0, 19).replace('T', ' '),
          operator_number: 'OP-HIST'
        });
      }
    });

    console.log(`📝 Generated ${historicalIssues.length} historical issues`);

    // Insert historical issues
    for (const issue of historicalIssues) {
      await pool.execute(
        `INSERT INTO issues 
         (machine_id, issue_type, title, description, status, reported_at, resolved_at, operator_number, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          issue.machine_id,
          issue.issue_type,
          issue.title,
          issue.description,
          issue.status,
          issue.reported_at,
          issue.resolved_at,
          issue.operator_number,
          issue.reported_at
        ]
      );
    }

    console.log(`✅ Added ${historicalIssues.length} historical issues`);

    // Calculate some statistics
    const [totalIssues] = await pool.execute(
      'SELECT COUNT(*) as count FROM issues WHERE status = "resolved"'
    );

    const [machinesWithIssues] = await pool.execute(
      'SELECT COUNT(DISTINCT machine_id) as count FROM issues'
    );

    console.log('\n📊 Historical Issues Data Summary:');
    console.log(`🔧 Total Resolved Issues: ${totalIssues[0].count}`);
    console.log(`🏭 Machines with Issues: ${machinesWithIssues[0].count}`);
    
    // Show sample MTBF/MTTR calculations for first machine
    const [sampleIssues] = await pool.execute(
      'SELECT * FROM issues WHERE machine_id = ? ORDER BY reported_at ASC',
      [machines[0].id]
    );

    if (sampleIssues.length >= 2) {
      // Calculate sample MTBF
      let totalDays = 0;
      for (let i = 1; i < sampleIssues.length; i++) {
        const prevDate = new Date(sampleIssues[i - 1].reported_at);
        const currDate = new Date(sampleIssues[i].reported_at);
        const daysDiff = (currDate - prevDate) / (1000 * 60 * 60 * 24);
        totalDays += daysDiff;
      }
      const mtbf = Math.round(totalDays / (sampleIssues.length - 1));

      // Calculate sample MTTR
      let totalHours = 0;
      sampleIssues.forEach(issue => {
        if (issue.resolved_at) {
          const reportedDate = new Date(issue.reported_at);
          const resolvedDate = new Date(issue.resolved_at);
          const hoursDiff = (resolvedDate - reportedDate) / (1000 * 60 * 60);
          totalHours += hoursDiff;
        }
      });
      const mttr = Math.round(totalHours / sampleIssues.length);

      console.log(`\n📈 Sample Analytics for ${machines[0].name}:`);
      console.log(`   MTBF: ${mtbf} days`);
      console.log(`   MTTR: ${mttr} hours`);
      console.log(`   Issues: ${sampleIssues.length}`);
    }
    
    console.log('\n✅ Historical issues data added successfully!');
    console.log('🔄 Refresh the analytics to see improved MTBF/MTTR calculations.');

  } catch (error) {
    console.error('❌ Error adding historical issues data:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
addHistoricalIssuesData();
