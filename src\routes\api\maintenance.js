import { Hono } from 'hono';
import { MaintenanceRequest } from '../../models/MaintenanceRequest.js';
import { MaintenanceMaterial } from '../../models/MaintenanceMaterial.js';
import { emailService } from '../../services/EmailService.js';
import { requireAuth } from '../../middleware/auth.js';

export const maintenanceApiRoutes = new Hono();

// GET /api/maintenance/statistics - Get maintenance statistics (must be before /:id route)
maintenanceApiRoutes.get('/statistics', async c => {
  try {
    const stats = await MaintenanceRequest.getStatistics();
    return c.json(stats);
  } catch (error) {
    console.error('Error fetching maintenance statistics:', error);
    return c.json({ error: 'Failed to fetch statistics' }, 500);
  }
});

// GET /api/maintenance/upcoming - Get upcoming maintenance
maintenanceApiRoutes.get('/upcoming', async c => {
  try {
    const query = c.req.query();
    const days = query.days ? parseInt(query.days) : 30;
    const upcoming = await MaintenanceRequest.getUpcomingMaintenance(days);
    return c.json(upcoming);
  } catch (error) {
    console.error('Error fetching upcoming maintenance:', error);
    return c.json({ error: 'Failed to fetch upcoming maintenance' }, 500);
  }
});

// GET /api/maintenance/overdue - Get overdue maintenance
maintenanceApiRoutes.get('/overdue', async c => {
  try {
    const overdue = await MaintenanceRequest.getOverdueMaintenance();
    return c.json(overdue);
  } catch (error) {
    console.error('Error fetching overdue maintenance:', error);
    return c.json({ error: 'Failed to fetch overdue maintenance' }, 500);
  }
});

// POST /api/maintenance - Create a new maintenance request
maintenanceApiRoutes.post('/', async c => {
  try {
    console.log('Creating maintenance request...');
    let body;
    let photoFile = null;

    // Check if request contains multipart/form-data (for file upload)
    const contentType = c.req.header('content-type');
    console.log('Content-Type:', contentType);

    if (contentType && contentType.includes('multipart/form-data')) {
      console.log('Processing FormData...');
      const formData = await c.req.formData();
      body = {};

      // Extract form fields
      for (const [key, value] of formData.entries()) {
        if (key === 'photo' && value instanceof File) {
          photoFile = value;
          console.log('Photo file found:', value.name, 'Size:', value.size, 'Type:', value.type);
        } else {
          body[key] = value;
        }
      }
      console.log('Form data processed. Body:', body);
    } else {
      console.log('Processing JSON...');
      body = await c.req.json();
    }

    // Validate required fields
    const { machine_id, operator_number, title } = body;

    if (!machine_id || !operator_number || !title) {
      return c.json(
        {
          error: 'Missing required fields: machine_id, operator_number, title',
        },
        400
      );
    }

    // Check for duplicates
    const duplicateCheck = await MaintenanceRequest.checkForDuplicates(body);
    if (duplicateCheck.isDuplicate) {
      return c.json(
        {
          error: duplicateCheck.message,
          isDuplicate: true,
          existingRequestId: duplicateCheck.existingRequest.id
        },
        409 // Conflict status code
      );
    }

    // Handle photo upload if present
    if (photoFile) {
      console.log('Processing photo upload...');
      // Validate file size (10MB limit)
      if (photoFile.size > 10 * 1024 * 1024) {
        console.log('Photo file too large:', photoFile.size);
        return c.json({ error: 'Photo file too large. Maximum size is 10MB.' }, 400);
      }

      // Validate file type
      if (!photoFile.type.startsWith('image/')) {
        console.log('Invalid file type:', photoFile.type);
        return c.json({ error: 'Invalid file type. Only images are allowed.' }, 400);
      }

      // Convert file to buffer for database storage
      console.log('Converting photo to buffer...');
      const photoBuffer = await photoFile.arrayBuffer();
      body.photo_filename = photoFile.name;
      body.photo_data = Buffer.from(photoBuffer);
      body.photo_mime_type = photoFile.type;
      body.photo_size = photoFile.size;
      console.log('Photo processed successfully. Buffer size:', body.photo_data.length);
    }

    console.log('Creating maintenance request with data:', {
      ...body,
      photo_data: body.photo_data ? '[BINARY DATA]' : null,
    });
    const maintenanceRequest = await MaintenanceRequest.create(body);

    // Send email notification for new maintenance request
    try {
      await emailService.notifyNewMaintenanceRequest(maintenanceRequest);
    } catch (error) {
      console.error('Failed to send new maintenance request notification:', error);
      // Don't fail the request if email fails
    }

    return c.json(maintenanceRequest, 201);
  } catch (error) {
    console.error('Error creating maintenance request:', error);
    return c.json({ error: 'Failed to create maintenance request' }, 500);
  }
});

// GET /api/maintenance - Get all maintenance requests with optional filtering
maintenanceApiRoutes.get('/', async c => {
  try {
    const query = c.req.query();
    const filters = {};

    // Parse query parameters for filtering
    if (query.machine_id) {
      filters.machine_id = parseInt(query.machine_id);
    }

    if (query.status) {
      filters.status = query.status;
    }

    if (query.urgency) {
      filters.urgency = query.urgency;
    }

    if (query.maintenance_type) {
      filters.maintenance_type = query.maintenance_type;
    }

    if (query.limit) {
      filters.limit = parseInt(query.limit);
    }

    const maintenanceRequests = await MaintenanceRequest.findAll(filters);
    return c.json(maintenanceRequests);
  } catch (error) {
    console.error('Error fetching maintenance requests:', error);
    return c.json({ error: 'Failed to fetch maintenance requests' }, 500);
  }
});

// GET /api/maintenance/:id/photo - Get maintenance request photo
maintenanceApiRoutes.get('/:id/photo', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const maintenanceRequest = await MaintenanceRequest.findById(id);

    if (!maintenanceRequest) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }

    if (!maintenanceRequest.photo_data || !maintenanceRequest.photo_mime_type) {
      return c.json({ error: 'Photo not found' }, 404);
    }

    // Set proper headers for image display
    c.header('Content-Type', maintenanceRequest.photo_mime_type);
    c.header('Content-Length', maintenanceRequest.photo_data.length.toString());
    c.header('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour

    return c.body(maintenanceRequest.photo_data);
  } catch (error) {
    console.error('Error fetching maintenance request photo:', error);
    return c.json({ error: 'Failed to fetch photo' }, 500);
  }
});

// GET /api/maintenance/:id - Get maintenance request by ID
maintenanceApiRoutes.get('/:id', async c => {
  try {
    const id = parseInt(c.req.param('id'));
    const maintenanceRequest = await MaintenanceRequest.findById(id);

    if (!maintenanceRequest) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }

    return c.json(maintenanceRequest);
  } catch (error) {
    console.error('Error fetching maintenance request:', error);
    return c.json({ error: 'Failed to fetch maintenance request' }, 500);
  }
});

// PUT /api/maintenance/:id - Update maintenance request (restricted to admin and maintenance roles)
maintenanceApiRoutes.put('/:id', requireAuth, async c => {
  const user = c.get('user');
  
  // Block viewer role from updating maintenance requests
  if (user.role === 'viewer') {
    return c.json({ error: 'Access denied' }, 403);
  }
  
  try {
    const id = parseInt(c.req.param('id'));
    const updateData = await c.req.json();

    // Get current maintenance request to compare status
    const currentRequest = await MaintenanceRequest.findById(id);
    if (!currentRequest) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }

    const maintenanceRequest = await MaintenanceRequest.update(id, updateData);

    if (!maintenanceRequest) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }

    // Send email notifications for status changes
    try {
      if (updateData.status && updateData.status !== currentRequest.status) {
        await emailService.notifyMaintenanceStatusChange(
          maintenanceRequest,
          currentRequest.status,
          updateData.status
        );

        // Special notification for scheduled maintenance
        if (updateData.status === 'scheduled') {
          await emailService.notifyMaintenanceScheduled(maintenanceRequest);
        }

        // Notify partner if assigned
        if (maintenanceRequest.partner_email) {
          await emailService.notifyPartnerStatusChange(
            maintenanceRequest,
            currentRequest.status,
            updateData.status
          );
        }
      }

      // Notify partner if newly assigned
      if (updateData.partner_id && updateData.partner_id !== currentRequest.partner_id) {
        await emailService.notifyPartnerAssignment(maintenanceRequest);
      }
    } catch (error) {
      console.error('Failed to send maintenance status change notification:', error);
      // Don't fail the request if email fails
    }

    return c.json(maintenanceRequest);
  } catch (error) {
    console.error('Error updating maintenance request:', error);
    return c.json({ error: 'Failed to update maintenance request' }, 500);
  }
});

// DELETE /api/maintenance/:id - Delete maintenance request (restricted to admin and maintenance roles)
maintenanceApiRoutes.delete('/:id', requireAuth, async c => {
  const user = c.get('user');
  
  // Block viewer role from deleting maintenance requests
  if (user.role === 'viewer') {
    return c.json({ error: 'Access denied' }, 403);
  }
  
  try {
    const id = parseInt(c.req.param('id'));
    const deleted = await MaintenanceRequest.delete(id);

    if (!deleted) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }

    return c.json({ message: 'Maintenance request deleted successfully' });
  } catch (error) {
    console.error('Error deleting maintenance request:', error);
    return c.json({ error: 'Failed to delete maintenance request' }, 500);
  }
});

// ===== MAINTENANCE MATERIALS ENDPOINTS =====

// GET /api/maintenance/:id/materials - Get materials for maintenance request
maintenanceApiRoutes.get('/:id/materials', async c => {
  try {
    const maintenanceRequestId = parseInt(c.req.param('id'));
    const materials = await MaintenanceMaterial.findByMaintenanceRequest(maintenanceRequestId);
    return c.json(materials);
  } catch (error) {
    console.error('Error fetching maintenance materials:', error);
    return c.json({ error: 'Failed to fetch materials' }, 500);
  }
});

// POST /api/maintenance/:id/materials - Add material to maintenance request
maintenanceApiRoutes.post('/:id/materials', async c => {
  try {
    const maintenanceRequestId = parseInt(c.req.param('id'));
    const materialData = await c.req.json();

    // Add maintenance_request_id to the data
    materialData.maintenance_request_id = maintenanceRequestId;

    const material = await MaintenanceMaterial.create(materialData);
    return c.json(material, 201);
  } catch (error) {
    console.error('Error adding maintenance material:', error);
    return c.json({ error: error.message || 'Failed to add material' }, 500);
  }
});

// PUT /api/maintenance/:id/materials/:materialId - Update material
maintenanceApiRoutes.put('/:id/materials/:materialId', async c => {
  try {
    const materialId = parseInt(c.req.param('materialId'));
    const materialData = await c.req.json();

    const material = await MaintenanceMaterial.update(materialId, materialData);

    if (!material) {
      return c.json({ error: 'Material not found' }, 404);
    }

    return c.json(material);
  } catch (error) {
    console.error('Error updating maintenance material:', error);
    return c.json({ error: error.message || 'Failed to update material' }, 500);
  }
});

// DELETE /api/maintenance/:id/materials/:materialId - Delete material
maintenanceApiRoutes.delete('/:id/materials/:materialId', async c => {
  try {
    const materialId = parseInt(c.req.param('materialId'));
    const deleted = await MaintenanceMaterial.delete(materialId);

    if (!deleted) {
      return c.json({ error: 'Material not found' }, 404);
    }

    return c.json({ message: 'Material deleted successfully' });
  } catch (error) {
    console.error('Error deleting maintenance material:', error);
    return c.json({ error: 'Failed to delete material' }, 500);
  }
});

// GET /api/maintenance/:id/materials/summary - Get materials summary
maintenanceApiRoutes.get('/:id/materials/summary', async c => {
  try {
    const maintenanceRequestId = parseInt(c.req.param('id'));
    const summary = await MaintenanceMaterial.getMaterialsSummary(maintenanceRequestId);
    return c.json(summary);
  } catch (error) {
    console.error('Error fetching materials summary:', error);
    return c.json({ error: 'Failed to fetch materials summary' }, 500);
  }
});

// POST /api/maintenance/:id/materials/update-stock - Update stock quantities when materials are used
maintenanceApiRoutes.post('/:id/materials/update-stock', async c => {
  try {
    const maintenanceRequestId = parseInt(c.req.param('id'));
    await MaintenanceMaterial.updateStockQuantities(maintenanceRequestId);
    return c.json({ message: 'Stock quantities updated successfully' });
  } catch (error) {
    console.error('Error updating stock quantities:', error);
    return c.json({ error: 'Failed to update stock quantities' }, 500);
  }
});

// POST /api/maintenance/schedule - Schedule maintenance from calendar
maintenanceApiRoutes.post('/schedule', requireAuth, async c => {
  try {
    const user = c.get('user');
    
    // Allow admin, maintenance roles only
    if (!['admin', 'maintenance'].includes(user.role)) {
      return c.json({ error: 'Forbidden' }, 403);
    }

    const body = await c.req.json();
    console.log('Scheduling maintenance from calendar:', body);

    // Validate required fields
    const { machine_id, title, scheduled_date } = body;
    if (!machine_id || !title || !scheduled_date) {
      return c.json(
        { error: 'Missing required fields: machine_id, title, scheduled_date' },
        400
      );
    }

    // Create maintenance request data
    const maintenanceData = {
      machine_id: parseInt(machine_id),
      title: title.trim(),
      description: body.description?.trim() || null,
      maintenance_type: body.maintenance_type || 'scheduled',
      priority: body.priority || 'medium',
      status: 'scheduled',
      scheduled_date: body.scheduled_date,
      due_date: body.due_date || null,
      estimated_duration_hours: body.estimated_duration_hours ? parseFloat(body.estimated_duration_hours) : null,
      estimated_cost: body.estimated_cost ? parseFloat(body.estimated_cost) : null,
      assigned_to: body.assigned_to?.trim() || null,
      requested_by: body.requested_by?.trim() || user.full_name || user.username,
      notes: body.notes?.trim() || null,
      operator_number: user.username, // Use scheduling user as operator
      created_by: user.username,
      created_at: new Date().toISOString()
    };

    console.log('Creating scheduled maintenance with data:', maintenanceData);

    // Use the scheduleMaintenanceFromCalendar method for proper validation
    const result = await MaintenanceRequest.scheduleMaintenanceFromCalendar(maintenanceData);

    console.log('Scheduled maintenance created with ID:', result.id);
    return c.json({
      success: true,
      message: 'Maintenance scheduled successfully',
      maintenance: result
    });

  } catch (error) {
    console.error('Error scheduling maintenance:', error);
    return c.json({ error: 'Failed to schedule maintenance: ' + error.message }, 500);
  }
});

// GET /api/maintenance/calendar/events - Get calendar events for date range
maintenanceApiRoutes.get('/calendar/events', requireAuth, async c => {
  try {
    const user = c.get('user');
    
    // Allow admin, maintenance roles only
    if (!['admin', 'maintenance'].includes(user.role)) {
      return c.json({ error: 'Forbidden' }, 403);
    }

    const query = c.req.query();
    const startDate = query.start;
    const endDate = query.end;

    if (!startDate || !endDate) {
      return c.json({ error: 'Missing start or end date parameters' }, 400);
    }

    const events = await MaintenanceRequest.getCalendarEvents(startDate, endDate);
    return c.json(events);

  } catch (error) {
    console.error('Error fetching calendar events:', error);
    return c.json({ error: 'Failed to fetch calendar events' }, 500);
  }
});

// GET /api/maintenance/calendar/stats - Get calendar statistics
maintenanceApiRoutes.get('/calendar/stats', requireAuth, async c => {
  try {
    const user = c.get('user');
    
    // Allow admin, maintenance roles only
    if (!['admin', 'maintenance'].includes(user.role)) {
      return c.json({ error: 'Forbidden' }, 403);
    }

    const query = c.req.query();
    const startDate = query.start;
    const endDate = query.end;

    if (!startDate || !endDate) {
      return c.json({ error: 'Missing start or end date parameters' }, 400);
    }

    const stats = await MaintenanceRequest.getCalendarStats(startDate, endDate);
    return c.json(stats);

  } catch (error) {
    console.error('Error fetching calendar statistics:', error);
    return c.json({ error: 'Failed to fetch calendar statistics' }, 500);
  }
});

// POST /api/maintenance/:id/start - Start maintenance from calendar
maintenanceApiRoutes.post('/:id/start', requireAuth, async c => {
  try {
    const user = c.get('user');
    const id = c.req.param('id');
    
    // Allow admin, maintenance, technician roles
    if (!['admin', 'maintenance', 'technician'].includes(user.role)) {
      return c.json({ error: 'Forbidden' }, 403);
    }

    const maintenance = await MaintenanceRequest.findById(id);
    if (!maintenance) {
      return c.json({ error: 'Maintenance request not found' }, 404);
    }

    if (maintenance.status !== 'scheduled') {
      return c.json({ error: 'Can only start scheduled maintenance' }, 400);
    }

    // Update maintenance status to in_progress
    const updateData = {
      status: 'in_progress'
      // Note: actual_start_date and started_by columns don't exist in current table structure
      // These would need to be added via database migration if tracking is needed
    };

    await MaintenanceRequest.update(id, updateData);

    console.log(`Maintenance ${id} started by ${user.username}`);
    return c.json({
      success: true,
      message: 'Maintenance started successfully'
    });

  } catch (error) {
    console.error('Error starting maintenance:', error);
    return c.json({ error: 'Failed to start maintenance: ' + error.message }, 500);
  }
});
