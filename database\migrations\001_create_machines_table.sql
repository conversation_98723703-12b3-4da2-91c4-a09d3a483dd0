-- Migration 001: Create machines table
-- Date: 2024-01-01

CREATE TABLE IF NOT EXISTS machines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_number VARCHAR(50) UNIQUE NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    manufacturer VA<PERSON><PERSON><PERSON>(255),
    model VARCHAR(255),
    serial_number VARCHAR(255),
    manufacturing_year INT,
    department VARCHAR(255),
    responsible_operator VARCHAR(255),
    location VARCHAR(255),
    status ENUM('online', 'offline', 'maintenance') DEFAULT 'online',
    last_maintenance DATE,
    next_maintenance DATE,
    warranty_end DATE,
    qr_code_data LONGBLOB,
    qr_code_mime_type VARCHAR(100) DEFAULT 'image/png',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_machines_machine_number ON machines(machine_number);
CREATE INDEX idx_machines_status ON machines(status);
CREATE INDEX idx_machines_department ON machines(department);
CREATE INDEX idx_machines_location ON machines(location);

-- Insert sample machines
INSERT INTO machines (machine_number, name, manufacturer, model, location, department, responsible_operator) VALUES
('M-001', 'CNC Freespink', 'Haas', 'VF-2', 'Tsehh A', 'Tootmine', 'Jaan Tamm'),
('M-002', 'Keevituspost 1', 'Lincoln Electric', 'Power Wave S350', 'Tsehh B', 'Keevitus', 'Mari Kask'),
('M-003', 'Hüdrauliline press', 'Schuler', 'PH 250', 'Tsehh A', 'Vormimistöö', 'Peeter Saar'),
('M-004', 'Lõikepink', 'Trumpf', 'TruLaser 3030', 'Tsehh C', 'Lõikamine', 'Anna Mets'),
('M-005', 'Kompressor', 'Atlas Copco', 'GA 22', 'Tehnohoone', 'Üldine', 'Toomas Kivi');
