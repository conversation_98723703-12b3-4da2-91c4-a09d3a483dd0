#!/usr/bin/env bun
import { User } from '../src/models/index.js';
import sequelize from '../src/database/connection.js';
import bcrypt from 'bcrypt';

// Uued turvalisemad paroolid
const newPasswords = {
  'admin': 'CmmsAdmin2024!',
  'jaan.tamm': 'Maintenance2024!',
  'mari.kask': 'Operator2024!',
  'peeter.saar': 'Viewer2024!'
};

async function updatePasswords() {
  try {
    console.log('🔐 Uuendan kasutajate paroole...');

    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Andmebaasiga ühendus loodud');

    // Sync the User model
    await User.sync();

    console.log('\n📋 Uuendatavad kasutajad:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    for (const [username, newPassword] of Object.entries(newPasswords)) {
      try {
        // Find user
        const user = await User.findOne({ where: { username } });

        if (!user) {
          console.log(`⚠️  Kasutajat '${username}' ei leitud`);
          continue;
        }

        // Hash new password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update user password and reset login attempts
        await user.update({
          password_hash: hashedPassword,
          failed_login_attempts: 0,
          locked_until: null,
          is_active: true
        });

        console.log(`✅ ${username} (${user.role}) - parool uuendatud`);

      } catch (userError) {
        console.error(`❌ Viga kasutaja ${username} uuendamisel:`, userError.message);
      }
    }

    console.log('\n🔑 Uued sisselogimise andmed:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    for (const [username, password] of Object.entries(newPasswords)) {
      const user = await User.findOne({ where: { username } });
      if (user) {
        console.log(`👤 ${username.padEnd(12)} | ${password.padEnd(18)} | ${user.role}`);
      }
    }

    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('\n⚠️  OLULINE: Salvesta need paroolid turvalisesse kohta!');
    console.log('🌐 Sisselogimise URL: http://localhost:8080/login');

  } catch (error) {
    console.error('❌ Viga paroolide uuendamisel:', error);

    if (error.name === 'SequelizeConnectionError') {
      console.error('Andmebaasiga ei saa ühendust. Kontrolli, et MariaDB töötab.');
    }

    process.exit(1);
  } finally {
    // Close database connection
    await sequelize.close();
  }
}

// Run the script
if (import.meta.main) {
  updatePasswords();
}

export { updatePasswords };