-- Migration 016: Create maintenance_requests table for maintenance planning
-- Date: 2025-08-12

CREATE TABLE IF NOT EXISTS maintenance_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    maintenance_type ENUM('regular', 'emergency', 'preventive', 'scheduled') DEFAULT 'scheduled',
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    
    -- Scheduling fields
    requested_date DATE,
    scheduled_date DATE,
    due_date DATE,
    estimated_duration_hours DECIMAL(5,2),
    
    -- Assignment
    assigned_to VARCHAR(255),
    assigned_partner_id INT,
    
    -- Request info
    operator_number VARCHAR(50),
    requested_by VARCHAR(255),
    approved_by VARCHAR(255),
    
    -- Status and completion
    status ENUM('requested', 'approved', 'scheduled', 'in_progress', 'completed', 'cancelled', 'rejected') DEFAULT 'requested',
    completed_date DATE,
    actual_duration_hours DECIMAL(5,2),
    
    -- Cost tracking
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    
    -- Materials and parts
    required_parts JSON,
    used_parts JSON,
    
    -- Additional info
    notes TEXT,
    completion_notes TEXT,
    
    -- Photo support
    before_photos JSON,
    after_photos JSON,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_partner_id) REFERENCES maintenance_partners(id) ON DELETE SET NULL
);

-- Create indexes for performance
CREATE INDEX idx_maintenance_requests_machine_id ON maintenance_requests(machine_id);
CREATE INDEX idx_maintenance_requests_status ON maintenance_requests(status);
CREATE INDEX idx_maintenance_requests_type ON maintenance_requests(maintenance_type);
CREATE INDEX idx_maintenance_requests_scheduled_date ON maintenance_requests(scheduled_date);
CREATE INDEX idx_maintenance_requests_due_date ON maintenance_requests(due_date);
CREATE INDEX idx_maintenance_requests_assigned_to ON maintenance_requests(assigned_to);
CREATE INDEX idx_maintenance_requests_priority ON maintenance_requests(priority);

-- Insert sample maintenance requests
INSERT INTO maintenance_requests (
    machine_id, title, description, maintenance_type, priority, 
    requested_date, scheduled_date, due_date, estimated_duration_hours,
    assigned_to, operator_number, requested_by, status, estimated_cost,
    notes
) VALUES
(1, 'CNC Freespingi kvartaline hooldus', 'Regulaarne preventivne hooldus: õlide vahetus, filtrite kontroll', 'preventive', 'medium', 
 '2025-08-10', '2025-08-15', '2025-08-20', 4.0, 
 'Jaan Tamm', 'OP001', 'Mari Kask', 'scheduled', 350.00,
 'Plaanitud kvartaline hooldus'),

(2, 'Keevitusposte elektrisüsteemi kontroll', 'Elektrilise süsteemi ülevaatus ja testimine', 'regular', 'low',
 '2025-08-12', '2025-08-18', '2025-08-25', 2.5,
 'Peeter Saar', 'OP002', 'Anna Mets', 'approved', 180.00,
 'Plaaniline elektrisüsteemi kontroll'),

(3, 'Hüdraulilise pressi kiire remont', 'Hüdraulikasüsteemi leke parandamine', 'emergency', 'high',
 '2025-08-11', '2025-08-13', '2025-08-14', 3.0,
 'Toomas Kivi', 'OP003', 'Jaan Tamm', 'in_progress', 420.00,
 'Kiireloomuline remont - hüdraulikaleke'),

(4, 'Kompressori hooldus', 'Kompressori filter ja õli vahetus', 'preventive', 'medium',
 '2025-08-08', '2025-08-20', '2025-08-22', 1.5,
 'Mari Kask', 'OP001', 'Peeter Saar', 'requested', 120.00,
 'Igakuine kompressori hooldus'),

(5, 'Lõikepingi täpsuse kalibreerimine', 'Lõikepingi mõõtmisseadmete kalibreerimine', 'scheduled', 'medium',
 '2025-08-09', '2025-08-25', '2025-08-30', 2.0,
 'Anna Mets', 'OP004', 'Toomas Kivi', 'scheduled', 250.00,
 'Aastane kalibreerimise hooldus')

ON DUPLICATE KEY UPDATE title = title;
