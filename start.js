#!/usr/bin/env bun
// start.js - Wrapper script for PM2 compatibility

import('./server.js').catch(error => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
EOF

# Make it executable
chmod +x start.js

# Start with the wrapper
pm2 start start.js \
  --name "cmms" \
  --interpreter "/root/.bun/bin/bun" \
  --env NODE_ENV=production \
  --env PORT=8080 \
  --env ALLOW_EXTERNAL_ACCESS=true \
  --env NOTIFICATIONS_ENABLED=true
