-- Migration 004: Create maintenance_records table (to match schema.sql)
-- Date: 2024-01-04

CREATE TABLE IF NOT EXISTS maintenance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    maintenance_type ENUM('regular', 'emergency', 'preventive') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    performed_by VARCHAR(255),
    scheduled_date DATE,
    completed_date DATE,
    duration_hours DECIMAL(5,2),
    cost DECIMAL(10,2),
    parts_used JSON,
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_maintenance_records_machine_id ON maintenance_records(machine_id);
CREATE INDEX idx_maintenance_records_status ON maintenance_records(status);
CREATE INDEX idx_maintenance_records_type ON maintenance_records(maintenance_type);
CREATE INDEX idx_maintenance_records_scheduled_date ON maintenance_records(scheduled_date);
CREATE INDEX idx_maintenance_records_completed_date ON maintenance_records(completed_date);

-- Insert sample maintenance records
INSERT INTO maintenance_records (machine_id, maintenance_type, title, description, performed_by, scheduled_date, completed_date, duration_hours, cost, parts_used, status, notes) VALUES
(1, 'preventive', 'CNC Freespingi kvartaline hooldus', 'Regulaarne preventivne hooldus: õlide vahetus, filtrite kontroll, kalibreerimine', 'Jaan Tamm', '2024-01-15', '2024-01-15', 4.5, 320.50, JSON_ARRAY('{"part": "Mootoriõli", "quantity": 5, "unit": "l"}', '{"part": "Õlifilter", "quantity": 1, "unit": "tk"}'), 'completed', 'Hooldus tehtud plaanipäraselt, kõik kontrollid OK'),
(2, 'emergency', 'Keevitusposte elektririkke parandus', 'Elektrilise süsteemi rikke kiire lahendamine', 'Mari Kask', '2024-01-20', '2024-01-20', 2.0, 150.00, JSON_ARRAY('{"part": "Kontaktor", "quantity": 1, "unit": "tk"}'), 'completed', 'Defektne kontaktor välja vahetatud'),
(3, 'regular', 'Hüdraulilise pressi hooldus', 'Igakuine hüdraulikasüsteemi kontrolli ja filtrite vahetus', 'Peeter Saar', '2024-02-01', '2024-02-01', 3.0, 280.75, JSON_ARRAY('{"part": "Hüdraulikafilter", "quantity": 2, "unit": "tk"}', '{"part": "Hüdraulikaõli", "quantity": 10, "unit": "l"}'), 'completed', 'Süsteem töökorras, õli ja filtrid vahetatud'),
(4, 'preventive', 'Lõikepingi kalibreerimine', 'Lõikepingi täpsuse kontrolli ja kalibreerimine', 'Anna Mets', '2024-02-15', NULL, NULL, NULL, NULL, 'scheduled', 'Plaanitud hooldus'),
(5, 'emergency', 'Kompressori rikke lahendamine', 'Kompressori ülemäära vibratsioon ja müra', 'Toomas Kivi', '2024-01-25', '2024-01-26', 6.0, 450.00, JSON_ARRAY('{"part": "Laager", "quantity": 2, "unit": "tk"}', '{"part": "Rihm", "quantity": 1, "unit": "tk"}'), 'completed', 'Defektsed laagrid ja rihm vahetatud, vibratsioon kõrvaldatud')
ON DUPLICATE KEY UPDATE title = title;
