-- Migration 001.5: Create operators table (to match schema.sql)
-- Date: 2024-01-01

CREATE TABLE IF NOT EXISTS operators (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operator_number VARCHAR(50) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255),
    department VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_operators_operator_number ON operators(operator_number);
CREATE INDEX idx_operators_department ON operators(department);

-- Insert sample operators
INSERT INTO operators (operator_number, name, email, department) VALUES
('OP-001', '<PERSON><PERSON>', '<EMAIL>', 'Tootmine'),
('OP-002', '<PERSON>', '<EMAIL>', 'Keevitus'),
('OP-003', '<PERSON>ee<PERSON>', '<EMAIL>', '<PERSON>ormim<PERSON>'),
('OP-004', '<PERSON>', '<EMAIL>', '<PERSON><PERSON><PERSON><PERSON>'),
('OP-005', '<PERSON><PERSON>', '<EMAIL>', 'Üldine')
ON DUPLICATE KEY UPDATE operator_number = operator_number;
