import { Hono } from 'hono';
import { 
  generateAdvancedAnalytics, 
  calculateCostAnalysis,
  calculateMTBF,
  calculateMTTR,
  calculateReliabilityScore,
  predictNextFailure,
  getMaintenanceRecommendation
} from '../../services/analytics.js';
import { Machine } from '../../models/Machine.js';
import { Issue } from '../../models/Issue.js';
import { MaintenanceRequest } from '../../models/MaintenanceRequest.js';
// import { requireAuth, requireRole } from '../../middleware/auth.js';
// import ExcelJS from 'exceljs';

const analyticsApiRoutes = new Hono();

// GET /api/analytics/advanced - Get comprehensive analytics data
analyticsApiRoutes.get('/advanced', async c => {
  try {
    const analytics = await generateAdvancedAnalytics();
    
    return c.json({
      success: true,
      costAnalysis: analytics.costAnalysis,
      predictiveAnalytics: analytics.predictiveAnalytics
    });
  } catch (error) {
    console.error('Error fetching advanced analytics:', error);
    return c.json({ success: false, error: 'Failed to fetch analytics data' }, 500);
  }
});

// GET /api/analytics/cost-analysis - Get detailed cost analysis
analyticsApiRoutes.get('/cost-analysis', async c => {
  try {
    const { startDate, endDate } = c.req.query();
    
    const start = startDate ? new Date(startDate) : new Date('2023-01-01');
    
    const end = endDate ? new Date(endDate) : new Date();
    
    const costAnalysis = await calculateCostAnalysis(start, end);
    
    // Add additional cost breakdown
    const costBreakdown = {
      maintenancePercentage: costAnalysis.totalMaintenanceCosts + costAnalysis.totalIssueCosts > 0 ?
        (costAnalysis.totalMaintenanceCosts / (costAnalysis.totalMaintenanceCosts + costAnalysis.totalIssueCosts) * 100) : 0,
      issuePercentage: costAnalysis.totalMaintenanceCosts + costAnalysis.totalIssueCosts > 0 ?
        (costAnalysis.totalIssueCosts / (costAnalysis.totalMaintenanceCosts + costAnalysis.totalIssueCosts) * 100) : 0
    };
    
    const totalCosts = costAnalysis.totalMaintenanceCosts + costAnalysis.totalIssueCosts;
    
    // Generate cost trends (monthly data)
    const costTrends = [];
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0');
      const monthLabel = date.toLocaleDateString('et-EE', { month: 'short', year: '2-digit' });
      
      months.push(monthLabel);
      costTrends.push({
        month: monthLabel,
        maintenance: costAnalysis.maintenanceCostsByMonth[monthKey] || 0,
        issues: costAnalysis.issueCostsByMonth[monthKey] || 0
      });
    }
    
    return c.json({
      success: true,
      data: {
        totalCosts,
        costBreakdown,
        costTrends,
        costPerMachine: costAnalysis.costPerMachine,
        roi: costAnalysis.roi
      }
    });
  } catch (error) {
    console.error('Error fetching cost analysis:', error);
    return c.json({ success: false, error: 'Failed to fetch cost analysis' }, 500);
  }
});

// GET /api/analytics/predictive - Get predictive analytics
analyticsApiRoutes.get('/predictive', async c => {
  try {
    const allMachines = await Machine.findAll();
    const machines = allMachines.filter(machine => machine.status !== 'deleted');

    const predictiveData = {};

    for (const machine of machines) {
      const allIssues = await Issue.findAll();
      const issues = allIssues.filter(issue => issue.machine_id === machine.id)
        .sort((a, b) => new Date(a.reported_at || a.created_at) - new Date(b.reported_at || b.created_at));

      const mtbf = calculateMTBF(issues);
      const mttr = calculateMTTR(issues);
      
      const machineData = {
        mtbf,
        mttr,
        issueCount: issues.length,
        totalDays: 180 // 6 months
      };

      const reliabilityScore = calculateReliabilityScore(machineData);
      const nextFailurePrediction = predictNextFailure(issues);
      const maintenanceRecommendation = getMaintenanceRecommendation({
        reliabilityScore,
        nextFailurePrediction
      });

      predictiveData[machine.id] = {
        machine_name: machine.name,
        machine_number: machine.machine_number,
        mtbf,
        mttr,
        reliabilityScore,
        nextFailurePrediction,
        maintenanceRecommendation
      };
    }

    return c.json({
      success: true,
      data: {
        machines: predictiveData
      }
    });
  } catch (error) {
    console.error('Error fetching predictive analytics:', error);
    return c.json({ success: false, error: 'Failed to fetch predictive analytics' }, 500);
  }
});

// GET /api/analytics/export - Export analytics data
analyticsApiRoutes.get('/export', async c => {
  try {
    const { format, type, startDate, endDate } = c.req.query();
    
    if (!format || !type) {
      return c.json({ success: false, error: 'Format and type parameters are required' }, 400);
    }

    let data;
    let filename;

    if (type === 'cost-analysis') {
      const start = startDate ? new Date(startDate) : (() => {
        const date = new Date();
        date.setMonth(date.getMonth() - 6);
        return date;
      })();
      
      const end = endDate ? new Date(endDate) : new Date();
      data = await calculateCostAnalysis(start, end);
      filename = `cost-analysis-${start.toISOString().split('T')[0]}-${end.toISOString().split('T')[0]}`;
    } else if (type === 'predictive') {
      const analytics = await generateAdvancedAnalytics();
      data = analytics.predictiveAnalytics;
      filename = `predictive-analytics-${new Date().toISOString().split('T')[0]}`;
    } else {
      return c.json({ success: false, error: 'Invalid export type' }, 400);
    }

    if (format === 'json') {
      c.header('Content-Type', 'application/json');
      c.header('Content-Disposition', `attachment; filename="${filename}.json"`);
      return c.json(data);
    } else if (format === 'excel') {
      // Excel export temporarily disabled - return CSV instead
      let csvContent = '';

      if (type === 'cost-analysis') {
        csvContent = 'Machine,Maintenance Cost,Issue Cost,Total Cost,ROI %\n';
        Object.values(data.costPerMachine).forEach(machine => {
          const roi = data.roi.byMachine[machine.machine_id] || 0;
          csvContent += `"${machine.machine_name}",${machine.maintenanceCost},${machine.issueCost},${machine.totalCost},${roi.toFixed(1)}\n`;
        });
      } else if (type === 'predictive') {
        csvContent = 'Machine,Machine Number,MTBF (days),MTTR (hours),Reliability %,Next Failure (days),Recommendation\n';
        Object.values(data).forEach(machine => {
          csvContent += `"${machine.machine_name}","${machine.machine_number}",${machine.mtbf || 'N/A'},${machine.mttr || 'N/A'},${machine.reliabilityScore},${machine.nextFailurePrediction?.daysFromNow || 'N/A'},"${machine.maintenanceRecommendation.action}"\n`;
        });
      }

      c.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      c.header('Content-Disposition', `attachment; filename="${filename}.csv"`);

      return c.text(csvContent);
    } else {
      return c.json({ success: false, error: 'Unsupported export format' }, 400);
    }
  } catch (error) {
    console.error('Error exporting analytics data:', error);
    return c.json({ success: false, error: 'Failed to export data' }, 500);
  }
});

export default analyticsApiRoutes;
