import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { app } from '../../server.js';
import { pool } from '../../src/config/database.js';

describe('QR Security API Integration', () => {
  let testMachineId;
  let testMachineSecret;

  beforeEach(async () => {
    // Clean up test data
    await pool.execute('DELETE FROM machines WHERE machine_number LIKE "QR-TEST-%"');
    await pool.execute('DELETE FROM qr_access_logs WHERE ip_address LIKE "test_%"');
    await pool.execute('DELETE FROM qr_rate_limits WHERE ip_address LIKE "test_%"');

    // Create test machine with secret
    testMachineSecret = 'test_secret_' + Math.random().toString(36).substring(2, 18);
    const [result] = await pool.execute(
      'INSERT INTO machines (machine_number, name, qr_secret, status) VALUES (?, ?, ?, ?)',
      ['QR-TEST-001', 'QR Test Machine', testMachineSecret, 'active']
    );
    testMachineId = result.insertId;
  });

  afterEach(async () => {
    // Clean up test data
    await pool.execute('DELETE FROM machines WHERE id = ?', [testMachineId]);
    await pool.execute('DELETE FROM qr_access_logs WHERE ip_address LIKE "test_%"');
    await pool.execute('DELETE FROM qr_rate_limits WHERE ip_address LIKE "test_%"');
  });

  describe('GET /secure/:machineNumber/:secret', () => {
    it('should redirect to operator page with valid secret', async () => {
      const response = await app.request(
        `/secure/QR-TEST-001/${testMachineSecret}`,
        {
          headers: {
            'X-Forwarded-For': '*************',
            'User-Agent': 'Test Browser'
          }
        }
      );

      expect(response.status).toBe(302);
      expect(response.headers.get('Location')).toBe('/operator/QR-TEST-001');
    });

    it('should return 404 with invalid secret', async () => {
      const response = await app.request(
        '/secure/QR-TEST-001/wrong_secret_123456789012',
        {
          headers: {
            'X-Forwarded-For': '*************',
            'User-Agent': 'Test Browser'
          }
        }
      );

      expect(response.status).toBe(404);
      const html = await response.text();
      expect(html).toContain('QR-kood ei ole kehtiv');
    });

    it('should return 404 with non-existent machine', async () => {
      const response = await app.request(
        '/secure/NONEXISTENT/any_secret_123456789012',
        {
          headers: {
            'X-Forwarded-For': '*************',
            'User-Agent': 'Test Browser'
          }
        }
      );

      expect(response.status).toBe(404);
      const html = await response.text();
      expect(html).toContain('QR-kood ei ole kehtiv');
    });

    it('should block external IP addresses', async () => {
      const response = await app.request(
        `/secure/QR-TEST-001/${testMachineSecret}`,
        {
          headers: {
            'X-Forwarded-For': '*******', // External IP
            'User-Agent': 'Test Browser'
          }
        }
      );

      expect(response.status).toBe(403);
      const html = await response.text();
      expect(html).toContain('Juurdepääs keelatud');
    });

    it('should enforce rate limiting', async () => {
      const ip = '*************';
      
      // Make 10 successful requests
      for (let i = 0; i < 10; i++) {
        const response = await app.request(
          `/secure/QR-TEST-001/${testMachineSecret}`,
          {
            headers: {
              'X-Forwarded-For': ip,
              'User-Agent': 'Test Browser'
            }
          }
        );
        expect(response.status).toBe(302);
      }

      // 11th request should be rate limited
      const response = await app.request(
        `/secure/QR-TEST-001/${testMachineSecret}`,
        {
          headers: {
            'X-Forwarded-For': ip,
            'User-Agent': 'Test Browser'
          }
        }
      );

      expect(response.status).toBe(429);
      const html = await response.text();
      expect(html).toContain('Liiga palju päringuid');
    });

    it('should log all access attempts', async () => {
      // Valid access
      await app.request(
        `/secure/QR-TEST-001/${testMachineSecret}`,
        {
          headers: {
            'X-Forwarded-For': 'test_192.168.1.104',
            'User-Agent': 'Test Browser Valid'
          }
        }
      );

      // Invalid access
      await app.request(
        '/secure/QR-TEST-001/wrong_secret_123456789012',
        {
          headers: {
            'X-Forwarded-For': 'test_192.168.1.105',
            'User-Agent': 'Test Browser Invalid'
          }
        }
      );

      // Check logs
      const [logs] = await pool.execute(
        'SELECT * FROM qr_access_logs WHERE ip_address LIKE "test_192.168.1.10%" ORDER BY created_at'
      );

      expect(logs).toHaveLength(2);
      
      // Valid access log
      expect(logs[0].access_type).toBe('valid');
      expect(logs[0].success).toBe(1);
      expect(logs[0].machine_number).toBe('QR-TEST-001');
      
      // Invalid access log
      expect(logs[1].access_type).toBe('invalid_secret');
      expect(logs[1].success).toBe(0);
      expect(logs[1].machine_number).toBe('QR-TEST-001');
    });
  });

  describe('POST /api/machines/:id/generate-qr-secret', () => {
    it('should generate new QR secret for machine', async () => {
      // This endpoint would be for admin use
      const response = await app.request(
        `/api/machines/${testMachineId}/generate-qr-secret`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      expect(response.status).toBe(200);
      const result = await response.json();
      expect(result.success).toBe(true);
      expect(result.secret).toHaveLength(32);
      expect(result.secret).not.toBe(testMachineSecret);
    });
  });

  describe('GET /admin/security/qr-logs', () => {
    it('should display QR access logs for admin', async () => {
      // Create some test logs
      await pool.execute(
        `INSERT INTO qr_access_logs 
         (machine_id, machine_number, secret_attempt, ip_address, user_agent, access_type, success) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [testMachineId, 'QR-TEST-001', testMachineSecret, 'test_192.168.1.200', 'Test Browser', 'valid', true]
      );

      const response = await app.request('/admin/security/qr-logs');
      
      expect(response.status).toBe(200);
      const html = await response.text();
      expect(html).toContain('QR-TEST-001');
      expect(html).toContain('test_192.168.1.200');
      expect(html).toContain('Valid access');
    });
  });
});
