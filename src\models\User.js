import { DataTypes } from 'sequelize';
import bcrypt from 'bcrypt';
import sequelize from '../database/connection.js';

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50],
      isAlphanumeric: true
    }
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  full_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [2, 100]
    }
  },
  role: {
    type: DataTypes.ENUM('admin', 'maintenance', 'operator', 'viewer'),
    allowNull: false,
    defaultValue: 'viewer'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  failed_login_attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  locked_until: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Instance methods
User.prototype.validatePassword = async function(password) {
  return await bcrypt.compare(password, this.password_hash);
};

User.prototype.isLocked = function() {
  return this.locked_until && new Date() < this.locked_until;
};

User.prototype.incrementFailedAttempts = async function() {
  this.failed_login_attempts += 1;
  
  // Lock account after 5 failed attempts for 15 minutes
  if (this.failed_login_attempts >= 5) {
    this.locked_until = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
  }
  
  await this.save();
};

User.prototype.resetFailedAttempts = async function() {
  this.failed_login_attempts = 0;
  this.locked_until = null;
  this.last_login_at = new Date();
  await this.save();
};

User.prototype.hasPermission = function(requiredRole) {
  const roleHierarchy = {
    'viewer': 1,
    'operator': 2,
    'maintenance': 3,
    'admin': 4
  };
  
  return roleHierarchy[this.role] >= roleHierarchy[requiredRole];
};

// Static methods
User.hashPassword = async function(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

User.findByUsername = async function(username) {
  return await this.findOne({ where: { username, is_active: true } });
};

User.findByEmail = async function(email) {
  return await this.findOne({ where: { email, is_active: true } });
};

// Validation for password strength
User.validatePasswordStrength = function(password) {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const errors = [];
  
  if (password.length < minLength) {
    errors.push(`Parool peab olema vähemalt ${minLength} tähemärki`);
  }
  if (!hasUpperCase) {
    errors.push('Parool peab sisaldama vähemalt ühte suurt tähte');
  }
  if (!hasLowerCase) {
    errors.push('Parool peab sisaldama vähemalt ühte väikest tähte');
  }
  if (!hasNumbers) {
    errors.push('Parool peab sisaldama vähemalt ühte numbrit');
  }
  if (!hasSpecialChar) {
    errors.push('Parool peab sisaldama vähemalt ühte erimärki');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export default User;
