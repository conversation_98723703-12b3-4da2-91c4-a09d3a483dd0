import { pool } from '../config/database.js';
import bcrypt from 'bcrypt';

export class User {
  constructor(data) {
    this.id = data.id;
    this.full_name = data.full_name;
    this.username = data.username;
    this.email = data.email;
    this.role = data.role;
    this.password_hash = data.password_hash;
    this.is_active = data.is_active;
    this.failed_login_attempts = data.failed_login_attempts || 0;
    this.locked_until = data.locked_until;
    this.last_login_at = data.last_login_at;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Static methods
  static async findByUsername(username) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM users WHERE username = ? AND is_active = 1',
        [username]
      );
      return rows.length > 0 ? new User(rows[0]) : null;
    } catch (error) {
      console.error('Error finding user by username:', error);
      return null;
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM users WHERE id = ?',
        [id]
      );
      return rows.length > 0 ? new User(rows[0]) : null;
    } catch (error) {
      console.error('Error finding user by ID:', error);
      return null;
    }
  }

  static async findAll(options = {}) {
    try {
      let query = 'SELECT * FROM users';
      let params = [];

      if (options.where) {
        const conditions = [];
        for (const [key, value] of Object.entries(options.where)) {
          conditions.push(`${key} = ?`);
          params.push(value);
        }
        if (conditions.length > 0) {
          query += ` WHERE ${conditions.join(' AND ')}`;
        }
      }

      if (options.order) {
        const orderClauses = options.order.map(([field, direction]) => `${field} ${direction}`);
        query += ` ORDER BY ${orderClauses.join(', ')}`;
      } else {
        query += ' ORDER BY created_at DESC';
      }

      const [rows] = await pool.execute(query, params);
      return rows.map(row => new User(row));
    } catch (error) {
      console.error('Error finding all users:', error);
      return [];
    }
  }

  static async create(userData) {
    try {
      const [result] = await pool.execute(
        `INSERT INTO users (full_name, username, email, role, password_hash, is_active) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          userData.full_name,
          userData.username,
          userData.email,
          userData.role,
          userData.password_hash,
          userData.is_active !== undefined ? userData.is_active : true
        ]
      );
      
      return await User.findById(result.insertId);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  static async hashPassword(password) {
    return await bcrypt.hash(password, 12);
  }

  static validatePasswordStrength(password) {
    const errors = [];
    
    if (password.length < 8) {
      errors.push('Parool peab olema vähemalt 8 tähemärki pikk');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Parool peab sisaldama vähemalt ühte suurt tähte');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Parool peab sisaldama vähemalt ühte väikest tähte');
    }
    
    if (!/[0-9]/.test(password)) {
      errors.push('Parool peab sisaldama vähemalt ühte numbrit');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Instance methods
  async validatePassword(password) {
    try {
      return await bcrypt.compare(password, this.password_hash);
    } catch (error) {
      console.error('Error validating password:', error);
      return false;
    }
  }

  async update(updateData) {
    try {
      const fields = [];
      const values = [];
      
      for (const [key, value] of Object.entries(updateData)) {
        if (key !== 'id') {
          fields.push(`${key} = ?`);
          values.push(value);
        }
      }
      
      values.push(this.id);
      
      await pool.execute(
        `UPDATE users SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      );
      
      // Refresh instance data
      const updated = await User.findById(this.id);
      Object.assign(this, updated);
      
      return this;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  async incrementFailedAttempts() {
    try {
      const newAttempts = this.failed_login_attempts + 1;
      const lockUntil = newAttempts >= 5 ? new Date(Date.now() + 15 * 60 * 1000) : null; // 15 minutes
      
      await pool.execute(
        'UPDATE users SET failed_login_attempts = ?, locked_until = ? WHERE id = ?',
        [newAttempts, lockUntil, this.id]
      );
      
      this.failed_login_attempts = newAttempts;
      this.locked_until = lockUntil;
    } catch (error) {
      console.error('Error incrementing failed attempts:', error);
    }
  }

  async resetFailedAttempts() {
    try {
      await pool.execute(
        'UPDATE users SET failed_login_attempts = 0, locked_until = NULL, last_login_at = CURRENT_TIMESTAMP WHERE id = ?',
        [this.id]
      );
      
      this.failed_login_attempts = 0;
      this.locked_until = null;
      this.last_login_at = new Date();
    } catch (error) {
      console.error('Error resetting failed attempts:', error);
    }
  }

  isLocked() {
    if (!this.locked_until) return false;
    return new Date() < new Date(this.locked_until);
  }

  hasPermission(permission) {
    // Simple role-based permissions
    const rolePermissions = {
      admin: ['*'], // Admin has all permissions
      maintenance: ['view_machines', 'edit_machines', 'view_issues', 'edit_issues', 'view_maintenance', 'edit_maintenance'],
      operator: ['view_machines', 'create_issues', 'view_projects', 'create_projects'],
      viewer: ['view_machines', 'view_issues', 'view_maintenance', 'view_projects']
    };
    
    const permissions = rolePermissions[this.role] || [];
    return permissions.includes('*') || permissions.includes(permission);
  }
}
