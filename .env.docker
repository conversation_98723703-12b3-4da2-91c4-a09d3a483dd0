# CMMS Docker Environment Configuration
# Use this file when running with Docker Compose

# Database Configuration (Docker)
DB_HOST=mariadb
DB_PORT=3306
DB_NAME=cmms_db
DB_USER=cmms_user
DB_PASSWORD=cmms_password

# Server Configuration
PORT=8080
NODE_ENV=development

# Application URL
BASE_URL=http://localhost:8080

# File Management
MAX_FILE_SIZE=10485760

# QR Code Settings
QR_CODE_SIZE=300

# Security (for production)
# SESSION_SECRET=your_session_secret_here
# JWT_SECRET=your_jwt_secret_here

# Email Configuration (same as main .env)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=CMMS System <<EMAIL>>

# Notification Settings
NOTIFICATIONS_ENABLED=true
ADMIN_EMAIL=<EMAIL>
MAINTENANCE_TEAM_EMAIL=<EMAIL>
CRITICAL_ISSUE_NOTIFICATIONS=true
MAINTENANCE_REMINDERS=true

# Development Settings
DEBUG=true
ENABLE_CORS=true
