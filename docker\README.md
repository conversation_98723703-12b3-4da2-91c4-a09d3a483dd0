# CMMS Docker Setup

This directory contains Docker configuration for the CMMS application database.

## Quick Start

1. **Start the database:**
   ```bash
   docker-compose up -d mariadb
   ```

2. **Start with phpMyAdmin (optional):**
   ```bash
   docker-compose up -d
   ```

3. **Stop the services:**
   ```bash
   docker-compose down
   ```

## Services

### MariaDB Database
- **Image:** mariadb:11.8
- **Port:** 3306 (mapped to host)
- **Database:** cmms_db
- **Root Password:** 67Bifmm23!
- **User:** cmms_user / cmms_password

### phpMyAdmin (Optional)
- **Port:** 8081 (http://localhost:8081)
- **Login:** root / 67Bifmm23!

## Database Initialization

The database is automatically initialized with:

1. **Schema** (`database/schema.sql`) - Basic table structure
2. **Migrations** (`docker/mariadb/init/02-run-migrations.sql`) - Additional tables
3. **Extended Migrations** (`docker/mariadb/init/03-additional-migrations.sql`) - Parts, materials, etc.
4. **Sample Data** (`docker/mariadb/init/04-sample-data.sql`) - Test data

## Data Persistence

Database data is stored in a Docker volume `mariadb_data` which persists between container restarts.

## Environment Variables

For Docker usage, copy `.env.docker` to `.env` or use it directly:

```bash
cp .env.docker .env
```

## Useful Commands

### View logs
```bash
docker-compose logs mariadb
docker-compose logs phpmyadmin
```

### Connect to database
```bash
docker-compose exec mariadb mysql -u root -p cmms_db
```

### Backup database
```bash
docker-compose exec mariadb mysqldump -u root -p67Bifmm23! cmms_db > backup.sql
```

### Restore database
```bash
docker-compose exec -T mariadb mysql -u root -p67Bifmm23! cmms_db < backup.sql
```

### Reset database (WARNING: Deletes all data)
```bash
docker-compose down -v
docker-compose up -d
```

## Troubleshooting

### Port 3306 already in use
If you have MySQL/MariaDB running locally, either:
1. Stop the local service
2. Change the port mapping in docker-compose.yml:
   ```yaml
   ports:
     - "3307:3306"  # Use port 3307 instead
   ```

### Permission issues
On Linux/Mac, you might need to adjust file permissions:
```bash
sudo chown -R 999:999 docker/mariadb/
```

### Database connection issues
1. Check if containers are running: `docker-compose ps`
2. Check logs: `docker-compose logs mariadb`
3. Verify network connectivity: `docker-compose exec mariadb ping mariadb`
