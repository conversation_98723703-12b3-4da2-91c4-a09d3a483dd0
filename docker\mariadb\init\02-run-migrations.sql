-- CMMS Database Migrations
-- This file runs all migrations in the correct order

USE cmms_db;

-- Migration 003: Create issues table (if not exists)
CREATE TABLE IF NOT EXISTS issues (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    operator_number VARCHAR(50) NOT NULL,
    issue_type ENUM('mechanical', 'electrical', 'hydraulic', 'software', 'other') NOT NULL,
    issue_category ENUM('issue', 'maintenance') NOT NULL,
    severity ENUM('low', 'medium', 'high') DEFAULT 'medium',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    photo_filename VARCHAR(255),
    photo_data LONGBLOB,
    photo_mime_type VARCHAR(100),
    status ENUM('new', 'in_progress', 'waiting', 'completed', 'cancelled') DEFAULT 'new',
    assigned_to VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    FOREIG<PERSON> KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);

-- Migration 004: Create maintenance_requests table
CREATE TABLE IF NOT EXISTS maintenance_requests (
  id INT AUTO_INCREMENT PRIMARY KEY,
  machine_id INT NOT NULL,
  operator_number VARCHAR(50) NOT NULL,
  operator_name VARCHAR(100),
  maintenance_type ENUM('preventive', 'corrective', 'emergency', 'inspection', 'calibration', 'cleaning', 'other') NOT NULL DEFAULT 'preventive',
  urgency ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
  title VARCHAR(200) NOT NULL,
  description TEXT,
  status ENUM('requested', 'scheduled', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'requested',
  requested_date DATE,
  scheduled_date DATE NULL,
  completed_date DATE NULL,
  assigned_to VARCHAR(100),
  maintenance_notes TEXT,
  completion_notes TEXT,
  estimated_duration INT,
  actual_duration INT,
  cost DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
  INDEX idx_machine_id (machine_id),
  INDEX idx_status (status),
  INDEX idx_urgency (urgency),
  INDEX idx_requested_date (requested_date),
  INDEX idx_scheduled_date (scheduled_date)
);

-- Create maintenance_attachments table
CREATE TABLE IF NOT EXISTS maintenance_attachments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  maintenance_request_id INT NOT NULL,
  filename VARCHAR(255) NOT NULL,
  original_filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size INT,
  mime_type VARCHAR(100),
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE CASCADE,
  INDEX idx_maintenance_request_id (maintenance_request_id)
);

-- Create maintenance_history table
CREATE TABLE IF NOT EXISTS maintenance_history (
  id INT AUTO_INCREMENT PRIMARY KEY,
  machine_id INT NOT NULL,
  maintenance_request_id INT,
  maintenance_type ENUM('preventive', 'corrective', 'emergency', 'inspection', 'calibration', 'cleaning', 'other') NOT NULL,
  performed_by VARCHAR(100),
  performed_date DATE NOT NULL,
  duration_minutes INT,
  cost DECIMAL(10,2),
  description TEXT,
  parts_used TEXT,
  next_maintenance_date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
  FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE SET NULL,
  INDEX idx_machine_id (machine_id),
  INDEX idx_performed_date (performed_date),
  INDEX idx_next_maintenance_date (next_maintenance_date)
);

-- Migration 005: Create maintenance partners table
CREATE TABLE IF NOT EXISTS maintenance_partners (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_name VARCHAR(255) NOT NULL,
  contact_person VARCHAR(255),
  email VARCHAR(255) NOT NULL UNIQUE,
  phone VARCHAR(50),
  address TEXT,
  specializations TEXT,
  hourly_rate DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'EUR',
  is_active BOOLEAN DEFAULT TRUE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_email (email),
  INDEX idx_active (is_active),
  INDEX idx_company_name (company_name)
);

-- Add partner fields to maintenance_requests if they don't exist
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'maintenance_requests' 
   AND column_name = 'partner_id' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE maintenance_requests ADD COLUMN partner_id INT NULL AFTER assigned_to, ADD COLUMN partner_notes TEXT NULL AFTER partner_id, ADD COLUMN partner_cost DECIMAL(10,2) NULL AFTER partner_notes',
  'SELECT "partner_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraint if it doesn't exist
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
   WHERE table_name = 'maintenance_requests' 
   AND column_name = 'partner_id' 
   AND constraint_name = 'fk_maintenance_partner'
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE maintenance_requests ADD CONSTRAINT fk_maintenance_partner FOREIGN KEY (partner_id) REFERENCES maintenance_partners(id) ON DELETE SET NULL',
  'SELECT "foreign key already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create index for partner_id if it doesn't exist
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
   WHERE table_name = 'maintenance_requests' 
   AND index_name = 'idx_partner_id'
   AND table_schema = DATABASE()) = 0,
  'CREATE INDEX idx_partner_id ON maintenance_requests(partner_id)',
  'SELECT "index already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
