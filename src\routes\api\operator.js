import { Hono } from 'hono';
import { Machine } from '../../models/Machine.js';

export const operatorApiRoutes = new Hono();

// GET /api/operator/:machineNumber - Get machine info by machine number for operator
operatorApiRoutes.get('/:machineNumber', async c => {
  try {
    const machineNumber = c.req.param('machineNumber');
    const machine = await Machine.findByMachineNumber(machineNumber);

    if (!machine) {
      return c.json({ error: 'Machine not found' }, 404);
    }

    // Add operator-specific information
    const operatorInfo = {
      ...machine,
      open_issues_count: 0, // TODO: Implement when issues are added
      last_maintenance: machine.last_maintenance,
      next_maintenance: machine.next_maintenance,
    };

    return c.json(operatorInfo);
  } catch (error) {
    console.error('Error fetching machine for operator:', error);
    return c.json({ error: 'Failed to fetch machine information' }, 500);
  }
});
