import { pool } from '../config/database.js';

export class MaintenanceMaterial {
  static async create(materialData) {
    try {
      const {
        maintenance_request_id,
        material_type,
        part_id = null,
        material_name = null,
        material_description = null,
        supplier = null,
        quantity = 1,
        unit_of_measure = 'tk',
        unit_cost = 0,
        status = 'planned',
        notes = null,
      } = materialData;

      // Validate required fields based on material type
      if (material_type === 'stock_part' && !part_id) {
        throw new Error('part_id is required for stock_part type');
      }
      if (material_type === 'external_material' && !material_name) {
        throw new Error('material_name is required for external_material type');
      }

      const [result] = await pool.execute(
        `INSERT INTO maintenance_materials (
          maintenance_request_id, material_type, part_id, material_name,
          material_description, supplier, quantity, unit_of_measure,
          unit_cost, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          maintenance_request_id,
          material_type,
          part_id,
          material_name,
          material_description,
          supplier,
          quantity,
          unit_of_measure,
          unit_cost,
          status,
          notes,
        ]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to create maintenance material: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          mm.*,
          p.name as part_name,
          p.part_number,
          p.category as part_category,
          p.manufacturer as part_manufacturer,
          p.quantity_in_stock,
          mr.title as maintenance_title,
          mr.status as maintenance_status
         FROM maintenance_materials mm
         LEFT JOIN parts p ON mm.part_id = p.id
         LEFT JOIN maintenance_requests mr ON mm.maintenance_request_id = mr.id
         WHERE mm.id = ?`,
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find maintenance material: ${error.message}`);
    }
  }

  static async findByMaintenanceRequest(maintenanceRequestId) {
    try {
      const [rows] = await pool.execute(
        `SELECT
          mm.*,
          p.name as part_name,
          p.part_number,
          p.category as part_category,
          p.manufacturer as part_manufacturer,
          p.quantity_in_stock,
          p.minimum_stock_level
         FROM maintenance_materials mm
         LEFT JOIN parts p ON mm.part_id = p.id
         WHERE mm.maintenance_request_id = ?
         ORDER BY mm.created_at ASC`,
        [maintenanceRequestId]
      );

      return rows;
    } catch (error) {
      throw new Error(`Failed to find materials for maintenance request: ${error.message}`);
    }
  }

  static async update(id, materialData) {
    try {
      const {
        material_type,
        part_id = null,
        material_name = null,
        material_description = null,
        supplier = null,
        quantity,
        unit_of_measure,
        unit_cost,
        status,
        notes = null,
      } = materialData;

      // Validate required fields based on material type
      if (material_type === 'stock_part' && !part_id) {
        throw new Error('part_id is required for stock_part type');
      }
      if (material_type === 'external_material' && !material_name) {
        throw new Error('material_name is required for external_material type');
      }

      await pool.execute(
        `UPDATE maintenance_materials SET
          material_type = ?, part_id = ?, material_name = ?,
          material_description = ?, supplier = ?, quantity = ?,
          unit_of_measure = ?, unit_cost = ?, status = ?, notes = ?
         WHERE id = ?`,
        [
          material_type,
          part_id,
          material_name,
          material_description,
          supplier,
          quantity,
          unit_of_measure,
          unit_cost,
          status,
          notes,
          id,
        ]
      );

      return await this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update maintenance material: ${error.message}`);
    }
  }

  static async delete(id) {
    try {
      const [result] = await pool.execute(
        'DELETE FROM maintenance_materials WHERE id = ?',
        [id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete maintenance material: ${error.message}`);
    }
  }

  static async getMaterialsSummary(maintenanceRequestId) {
    try {
      const [rows] = await pool.execute(
        `SELECT * FROM maintenance_materials_summary WHERE maintenance_request_id = ?`,
        [maintenanceRequestId]
      );

      return rows[0] || {
        maintenance_request_id: maintenanceRequestId,
        total_materials: 0,
        stock_parts_count: 0,
        external_materials_count: 0,
        total_materials_cost: 0,
        stock_parts_cost: 0,
        external_materials_cost: 0,
      };
    } catch (error) {
      throw new Error(`Failed to get materials summary: ${error.message}`);
    }
  }

  static async updateStockQuantities(maintenanceRequestId) {
    try {
      // Get all stock parts used in this maintenance request
      const [materials] = await pool.execute(
        `SELECT mm.part_id, mm.quantity
         FROM maintenance_materials mm
         WHERE mm.maintenance_request_id = ? 
         AND mm.material_type = 'stock_part' 
         AND mm.status = 'used'
         AND mm.part_id IS NOT NULL`,
        [maintenanceRequestId]
      );

      // Update stock quantities for each part
      for (const material of materials) {
        await pool.execute(
          `UPDATE parts SET quantity_in_stock = quantity_in_stock - ? WHERE id = ?`,
          [material.quantity, material.part_id]
        );
      }

      return true;
    } catch (error) {
      throw new Error(`Failed to update stock quantities: ${error.message}`);
    }
  }
}
