-- Migration: Create documents table for file management
-- Epic 5: Dokumentide Haldus

-- Create documents table
CREATE TABLE IF NOT EXISTS documents (
  id INT AUTO_INCREMENT PRIMARY KEY,
  machine_id INT NOT NULL,
  title VARCHAR(255) NOT NULL COMMENT '<PERSON><PERSON><PERSON><PERSON> pealkiri',
  description TEXT COMMENT 'Dokumendi kirjeldus',
  document_type ENUM('manual', 'schematic', 'photo', 'certificate', 'maintenance_log', 'other') NOT NULL DEFAULT 'other' COMMENT '<PERSON><PERSON><PERSON>di tüüp',
  file_name VARCHAR(255) NOT NULL COMMENT '<PERSON>aal failinimi',
  file_data LONGBLOB NOT NULL COMMENT '<PERSON>ail<PERSON> sisu (binary data)',
  file_size INT NOT NULL COMMENT 'Faili suurus baitides',
  mime_type VARCHAR(100) NOT NULL COMMENT 'Faili MIME tüüp',
  file_extension VARCHAR(10) NOT NULL COMMENT '<PERSON>aili laiend',
  uploaded_by VARCHAR(100) COMMENT 'Kes faili üles laadis',
  version VARCHAR(20) DEFAULT '1.0' COMMENT 'Dokumendi versioon',
  is_active BOOLEAN DEFAULT TRUE COMMENT 'Kas dokument on aktiivne',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Foreign key constraints
  CONSTRAINT fk_documents_machine
    FOREIGN KEY (machine_id) REFERENCES machines(id)
    ON DELETE CASCADE,

  -- Indexes for performance
  INDEX idx_documents_machine_id (machine_id),
  INDEX idx_documents_type (document_type),
  INDEX idx_documents_active (is_active),
  INDEX idx_documents_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Masinate dokumentide haldus';

-- Create document_access_log table for audit trail
CREATE TABLE IF NOT EXISTS document_access_log (
  id INT AUTO_INCREMENT PRIMARY KEY,
  document_id INT NOT NULL,
  user_identifier VARCHAR(100) COMMENT 'Kasutaja identifikaator (IP, session, jne)',
  action ENUM('view', 'download', 'upload', 'delete') NOT NULL COMMENT 'Tegevuse tüüp',
  user_agent TEXT COMMENT 'Brauseri info',
  ip_address VARCHAR(45) COMMENT 'IP aadress',
  accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- Foreign key constraints
  CONSTRAINT fk_document_access_document
    FOREIGN KEY (document_id) REFERENCES documents(id)
    ON DELETE CASCADE,

  -- Indexes
  INDEX idx_access_log_document (document_id),
  INDEX idx_access_log_action (action),
  INDEX idx_access_log_date (accessed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Dokumentide ligipääsu logi';

-- Insert sample documents for testing (without actual file data for now)
INSERT INTO documents (machine_id, title, description, document_type, file_name, file_data, file_size, mime_type, file_extension, uploaded_by, version) VALUES
(1, 'HAAS VF-2 Kasutusjuhend', 'Masina põhiline kasutusjuhend ja ohutuse juhised', 'manual', 'haas_vf2_manual.pdf', 'sample_pdf_data', 2048576, 'application/pdf', 'pdf', 'admin', '2.1'),
(1, 'Elektriskeemi joonis', 'Masina elektriskeemi detailne joonis', 'schematic', 'vf2_electrical_schematic.pdf', 'sample_pdf_data', 1536000, 'application/pdf', 'pdf', 'admin', '1.0'),
(1, 'Hoolduse foto', 'Foto viimasest hooldusest', 'photo', 'maintenance_photo_2024.jpg', 'sample_image_data', 512000, 'image/jpeg', 'jpg', 'tehnik1', '1.0'),
(2, 'Tornpingi juhend', 'CNC tornpingi kasutusjuhend', 'manual', 'lathe_manual.pdf', 'sample_pdf_data', 3072000, 'application/pdf', 'pdf', 'admin', '1.5');

-- Note: Files are stored directly in database as LONGBLOB
-- This ensures all data is included in database backups
