import { Hono } from 'hono';
import { Notification } from '../../models/Notification.js';
import { User } from '../../models/User.js';
import { Machine } from '../../models/Machine.js';
import { requireAuth } from '../../middleware/auth.js';
import { pool } from '../../config/database.js';

export const appNotificationApiRoutes = new Hono();

// Apply auth middleware to all routes
appNotificationApiRoutes.use('/*', requireAuth);

// GET /api/app-notifications - Get user's notifications
appNotificationApiRoutes.get('/', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const { limit = 20, unread_only } = c.req.query();
    const options = {
      limit: parseInt(limit),
      unreadOnly: unread_only === 'true'
    };

    const notifications = await Notification.findByUserId(user.id, options);
    const unreadCount = await Notification.getUnreadCount(user.id);

    return c.json({
      success: true,
      notifications,
      unread_count: unreadCount
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return c.json({ error: 'Failed to fetch notifications' }, 500);
  }
});

// POST /api/app-notifications - Send a new notification
appNotificationApiRoutes.post('/', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const { to_user_id, to_roles, title, message, type, machine_id } = await c.req.json();

    if (!title || !message) {
      return c.json({ error: 'Title and message are required' }, 400);
    }

    let notifications = [];

    if (to_user_id) {
      // Send to specific user
      const notification = await Notification.sendToUser(
        user.id,
        to_user_id,
        title,
        message,
        type,
        machine_id
      );
      notifications.push(notification);
    } else if (to_roles && to_roles.length > 0) {
      // Send to users with specific roles
      notifications = await Notification.sendToRoles(
        user.id,
        to_roles,
        title,
        message,
        type,
        machine_id
      );
    } else {
      return c.json({ error: 'Must specify either to_user_id or to_roles' }, 400);
    }

    return c.json({
      success: true,
      message: 'Notification(s) sent successfully',
      notifications
    });
  } catch (error) {
    console.error('Error sending notification:', error);
    return c.json({ error: 'Failed to send notification' }, 500);
  }
});

// PUT /api/app-notifications/:id/read - Mark notification as read
appNotificationApiRoutes.put('/:id/read', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const id = parseInt(c.req.param('id'));
    const success = await Notification.markAsRead(id, user.id);

    if (success) {
      return c.json({ success: true, message: 'Notification marked as read' });
    } else {
      return c.json({ error: 'Notification not found or already read' }, 404);
    }
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return c.json({ error: 'Failed to mark notification as read' }, 500);
  }
});

// PUT /api/app-notifications/read-all - Mark all notifications as read
appNotificationApiRoutes.put('/read-all', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const count = await Notification.markAllAsRead(user.id);

    return c.json({
      success: true,
      message: `Marked ${count} notifications as read`
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return c.json({ error: 'Failed to mark notifications as read' }, 500);
  }
});

// DELETE /api/app-notifications/:id - Delete notification
appNotificationApiRoutes.delete('/:id', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const id = parseInt(c.req.param('id'));
    const success = await Notification.delete(id, user.id);

    if (success) {
      return c.json({ success: true, message: 'Notification deleted' });
    } else {
      return c.json({ error: 'Notification not found' }, 404);
    }
  } catch (error) {
    console.error('Error deleting notification:', error);
    return c.json({ error: 'Failed to delete notification' }, 500);
  }
});

// GET /api/app-notifications/users - Get list of users to send notifications to
appNotificationApiRoutes.get('/users', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    // Get all users except current user
    const [users] = await pool.execute(
      'SELECT id, username, full_name, role FROM users WHERE id != ? ORDER BY full_name',
      [user.id]
    );

    // Group users by role for easier selection
    const usersByRole = {
      admin: [],
      maintenance: [],
      operator: [],
      viewer: []
    };

    users.forEach(u => {
      if (usersByRole[u.role]) {
        usersByRole[u.role].push(u);
      }
    });

    return c.json({
      success: true,
      users,
      users_by_role: usersByRole
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return c.json({ error: 'Failed to fetch users' }, 500);
  }
});

// GET /api/app-notifications/machines - Get list of machines for context
appNotificationApiRoutes.get('/machines', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const machines = await Machine.findAll({ is_active: true });

    return c.json({
      success: true,
      machines: machines.map(m => ({
        id: m.id,
        machine_number: m.machine_number,
        name: m.name,
        location: m.location
      }))
    });
  } catch (error) {
    console.error('Error fetching machines:', error);
    return c.json({ error: 'Failed to fetch machines' }, 500);
  }
});

// DELETE /api/app-notifications/:id - Delete notification (admin only)
appNotificationApiRoutes.delete('/:id', async c => {
  try {
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    // Only admin can delete notifications
    if (user.role !== 'admin') {
      return c.json({ error: 'Admin access required' }, 403);
    }

    const id = parseInt(c.req.param('id'));
    const success = await Notification.delete(id);

    if (success) {
      return c.json({ success: true, message: 'Notification deleted successfully' });
    } else {
      return c.json({ error: 'Notification not found' }, 404);
    }
  } catch (error) {
    console.error('Error deleting notification:', error);
    return c.json({ error: 'Failed to delete notification' }, 500);
  }
});
