const { test, expect } = require('@playwright/test');

test.describe('Advanced Reporting and Analytics', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('[name="username"]', 'admin');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');
    await expect(page).toHaveURL('/admin');
  });

  test('should display advanced analytics dashboard', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Check page title and main sections
    await expect(page.locator('h2')).toContainText('Aruanded ja Analüütika');
    
    // Check cost analysis section
    await expect(page.locator('h3')).toContainText('Kulude analüüs');
    await expect(page.locator('#total-maintenance-costs')).toBeVisible();
    await expect(page.locator('#total-issue-costs')).toBeVisible();
    await expect(page.locator('#total-costs')).toBeVisible();
    
    // Check predictive analytics section
    await expect(page.locator('h3')).toContainText('Ennustav analüütika');
    await expect(page.locator('#average-mtbf')).toBeVisible();
    await expect(page.locator('#average-mttr')).toBeVisible();
    await expect(page.locator('#average-reliability')).toBeVisible();
  });

  test('should load and display cost analysis data', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Wait for analytics to load
    await page.waitForFunction(() => {
      const totalCosts = document.getElementById('total-costs');
      return totalCosts && totalCosts.textContent !== '€0';
    }, { timeout: 10000 });
    
    // Check that cost data is displayed
    const totalMaintenanceCosts = await page.locator('#total-maintenance-costs').textContent();
    const totalIssueCosts = await page.locator('#total-issue-costs').textContent();
    const totalCosts = await page.locator('#total-costs').textContent();
    
    expect(totalMaintenanceCosts).toMatch(/€\d+/);
    expect(totalIssueCosts).toMatch(/€\d+/);
    expect(totalCosts).toMatch(/€\d+/);
  });

  test('should display cost trends chart', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Wait for chart to load
    await page.waitForSelector('#cost-trends-chart', { timeout: 10000 });
    
    // Check that chart canvas is present and has content
    const chart = page.locator('#cost-trends-chart');
    await expect(chart).toBeVisible();
    
    // Verify chart has been rendered (canvas should have some drawing)
    const chartExists = await page.evaluate(() => {
      const canvas = document.getElementById('cost-trends-chart');
      const ctx = canvas.getContext('2d');
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      return imageData.data.some(pixel => pixel !== 0);
    });
    
    expect(chartExists).toBe(true);
  });

  test('should display predictive analytics data', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Wait for predictive analytics to load
    await page.waitForFunction(() => {
      const mtbf = document.getElementById('average-mtbf');
      return mtbf && mtbf.textContent !== '- päeva';
    }, { timeout: 10000 });
    
    // Check MTBF, MTTR, and reliability metrics
    const avgMTBF = await page.locator('#average-mtbf').textContent();
    const avgMTTR = await page.locator('#average-mttr').textContent();
    const avgReliability = await page.locator('#average-reliability').textContent();
    
    expect(avgMTBF).toMatch(/\d+ päeva/);
    expect(avgMTTR).toMatch(/\d+ tundi/);
    expect(avgReliability).toMatch(/\d+%/);
  });

  test('should display reliability distribution chart', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Wait for reliability chart to load
    await page.waitForSelector('#reliability-chart', { timeout: 10000 });
    
    const chart = page.locator('#reliability-chart');
    await expect(chart).toBeVisible();
    
    // Verify doughnut chart has been rendered
    const chartExists = await page.evaluate(() => {
      const canvas = document.getElementById('reliability-chart');
      const ctx = canvas.getContext('2d');
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      return imageData.data.some(pixel => pixel !== 0);
    });
    
    expect(chartExists).toBe(true);
  });

  test('should display machine predictions table', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Wait for predictions table to load
    await page.waitForSelector('#predictions-table tr', { timeout: 10000 });
    
    // Check table headers
    await expect(page.locator('th')).toContainText('Masin');
    await expect(page.locator('th')).toContainText('MTBF (päeva)');
    await expect(page.locator('th')).toContainText('MTTR (tundi)');
    await expect(page.locator('th')).toContainText('Järgmine rike');
    await expect(page.locator('th')).toContainText('Töökindlus');
    await expect(page.locator('th')).toContainText('Soovitus');
    
    // Check that table has data rows
    const rowCount = await page.locator('#predictions-table tr').count();
    expect(rowCount).toBeGreaterThan(0);
  });

  test('should display cost per machine table', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Wait for cost per machine table to load
    await page.waitForSelector('#cost-per-machine-table tr', { timeout: 10000 });
    
    // Check table headers
    await expect(page.locator('th')).toContainText('Masin');
    await expect(page.locator('th')).toContainText('Hooldus');
    await expect(page.locator('th')).toContainText('Rikked');
    await expect(page.locator('th')).toContainText('Kokku');
    await expect(page.locator('th')).toContainText('ROI');
    
    // Check that table has data rows
    const rowCount = await page.locator('#cost-per-machine-table tr').count();
    expect(rowCount).toBeGreaterThan(0);
  });

  test('should filter reports by date range', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Set date filters
    const startDate = '2024-01-01';
    const endDate = '2024-01-31';
    
    await page.fill('#start-date', startDate);
    await page.fill('#end-date', endDate);
    
    // Generate reports with filters
    await page.click('button:has-text("Genereeri Aruanded")');
    
    // Wait for loading to complete
    await page.waitForSelector('#loading', { state: 'hidden', timeout: 15000 });
    
    // Check that export buttons become visible
    await expect(page.locator('#export-buttons')).toBeVisible();
  });

  test('should filter reports by machine group', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Select a machine group if available
    const groupOptions = await page.locator('#group-filter option').count();
    if (groupOptions > 1) {
      await page.selectOption('#group-filter', { index: 1 });
      
      // Generate reports with group filter
      await page.click('button:has-text("Genereeri Aruanded")');
      
      // Wait for loading to complete
      await page.waitForSelector('#loading', { state: 'hidden', timeout: 15000 });
      
      // Verify that filtered data is displayed
      await expect(page.locator('#export-buttons')).toBeVisible();
    }
  });

  test('should export reports to PDF', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Generate reports first
    await page.click('button:has-text("Genereeri Aruanded")');
    await page.waitForSelector('#export-buttons', { timeout: 15000 });
    
    // Click PDF dropdown
    await page.click('button:has-text("PDF")');
    await expect(page.locator('#pdf-dropdown')).toBeVisible();
    
    // Test PDF download
    const downloadPromise = page.waitForEvent('download');
    await page.click('button:has-text("Lae alla")');
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toMatch(/\.pdf$/);
  });

  test('should export reports to Excel', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Generate reports first
    await page.click('button:has-text("Genereeri Aruanded")');
    await page.waitForSelector('#export-buttons', { timeout: 15000 });
    
    // Test Excel export
    const downloadPromise = page.waitForEvent('download');
    await page.click('button:has-text("Excel")');
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toMatch(/\.xlsx$/);
  });

  test('should print reports', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Generate reports first
    await page.click('button:has-text("Genereeri Aruanded")');
    await page.waitForSelector('#export-buttons', { timeout: 15000 });
    
    // Mock print dialog
    await page.evaluate(() => {
      window.print = () => console.log('Print dialog opened');
    });
    
    // Click print button
    await page.click('button:has-text("Prindi")');
    
    // Verify print function was called (in real scenario, this would open print dialog)
    const printCalled = await page.evaluate(() => {
      return window.print.toString().includes('Print dialog opened');
    });
    
    expect(printCalled).toBe(true);
  });

  test('should show maintenance recommendations based on reliability scores', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Wait for predictions table to load
    await page.waitForSelector('#predictions-table tr', { timeout: 10000 });
    
    // Check that recommendations are displayed
    const recommendations = await page.locator('#predictions-table .inline-flex').allTextContents();
    
    // Verify that recommendations contain expected values
    const validRecommendations = ['Kiire sekkumine', 'Ennetav hooldus', 'Jälgimine', 'Heas korras'];
    recommendations.forEach(rec => {
      expect(validRecommendations).toContain(rec);
    });
  });

  test('should calculate and display ROI correctly', async ({ page }) => {
    await page.goto('/admin/reports');
    
    // Wait for cost per machine table to load
    await page.waitForSelector('#cost-per-machine-table tr', { timeout: 10000 });
    
    // Check that ROI values are displayed and formatted correctly
    const roiCells = await page.locator('#cost-per-machine-table td:nth-child(5)').allTextContents();
    
    roiCells.forEach(roi => {
      expect(roi).toMatch(/^-?\d+\.\d%$/); // Format: number.number%
    });
  });

  test('should be accessible to viewer role', async ({ page }) => {
    // Logout and login as viewer
    await page.goto('/auth/logout');
    await page.goto('/login');
    await page.fill('[name="username"]', 'peeter.saar');
    await page.fill('[name="password"]', 'admin123');
    await page.click('[type="submit"]');
    
    // Navigate to reports
    await page.goto('/admin/reports');
    
    // Verify viewer can access reports
    await expect(page.locator('h2')).toContainText('Aruanded ja Analüütika');
    
    // Verify analytics data loads for viewer
    await page.waitForFunction(() => {
      const totalCosts = document.getElementById('total-costs');
      return totalCosts && totalCosts.textContent !== '€0';
    }, { timeout: 10000 });
  });

  test('should handle empty data gracefully', async ({ page }) => {
    // This test assumes a clean database or filtered view with no data
    await page.goto('/admin/reports');
    
    // Set date range that has no data
    await page.fill('#start-date', '2020-01-01');
    await page.fill('#end-date', '2020-01-31');
    await page.click('button:has-text("Genereeri Aruanded")');
    
    // Wait for loading to complete
    await page.waitForSelector('#loading', { state: 'hidden', timeout: 15000 });
    
    // Verify that the page handles empty data gracefully
    await expect(page.locator('#total-costs')).toContainText('€0');
    await expect(page.locator('#average-mtbf')).toContainText('0 päeva');
  });
});
