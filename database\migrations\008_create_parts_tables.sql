-- Varuosade halduse tabelid
-- Migration 007: Parts Management System

-- 1. Varuosade põhitabel
CREATE TABLE IF NOT EXISTS parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    part_number VARCHAR(50) UNIQUE NOT NULL COMMENT 'Varuosa number (nt. PART-001)',
    name VARCHAR(255) NOT NULL COMMENT 'Varuosa nimi',
    description TEXT COMMENT 'Detailne kirjeldus',
    category VARCHAR(100) NOT NULL COMMENT 'Kategooria (mechanical, electrical, hydraulic, etc.)',
    manufacturer VARCHAR(255) COMMENT 'Tootja',
    model VARCHAR(255) COMMENT 'Mudel/tüüp',
    unit_of_measure VARCHAR(50) DEFAULT 'tk' COMMENT 'M<PERSON>õtühik (tk, m, kg, l, etc.)',
    unit_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '<PERSON><PERSON>ku hind eurodes',
    supplier VARCHAR(255) COMMENT 'Peamine tarnija',
    supplier_part_number VARCHAR(100) COMMENT 'Tarnija varuosa number',
    location VARCHAR(255) COMMENT 'Asukoht laos',
    
    -- Laoseisu andmed
    quantity_in_stock INT DEFAULT 0 COMMENT 'Praegune laokogus',
    minimum_stock_level INT DEFAULT 0 COMMENT 'Minimaalne laokogus',
    maximum_stock_level INT DEFAULT 0 COMMENT 'Maksimaalne laokogus',
    reorder_point INT DEFAULT 0 COMMENT 'Tellimise punkt',
    
    -- Metaandmed
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Kas varuosa on aktiivne',
    notes TEXT COMMENT 'Lisainfo ja märkused',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_part_number (part_number),
    INDEX idx_category (category),
    INDEX idx_active (is_active),
    INDEX idx_low_stock (quantity_in_stock, minimum_stock_level)
);

-- 2. Masina-varuosa seosed
CREATE TABLE IF NOT EXISTS machine_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    part_id INT NOT NULL,
    quantity_needed INT DEFAULT 1 COMMENT 'Vajalik kogus selle masina jaoks',
    is_critical BOOLEAN DEFAULT FALSE COMMENT 'Kas on kriitilise tähtsusega osa',
    replacement_interval_hours INT COMMENT 'Vahetamise intervall tundides',
    last_replaced_at TIMESTAMP NULL COMMENT 'Viimane vahetamise aeg',
    notes TEXT COMMENT 'Märkused selle masina ja osa kohta',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
    FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_machine_part (machine_id, part_id),
    INDEX idx_machine (machine_id),
    INDEX idx_part (part_id),
    INDEX idx_critical (is_critical)
);

-- 3. Varuosade kasutamise ajalugu
CREATE TABLE IF NOT EXISTS part_usage_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    part_id INT NOT NULL,
    machine_id INT NULL COMMENT 'Masin, kus osa kasutati (võib olla NULL üldise kasutamise korral)',
    maintenance_request_id INT NULL COMMENT 'Hoolduse taotlus, mille käigus osa kasutati',
    issue_id INT NULL COMMENT 'Rike, mille lahendamisel osa kasutati',
    
    quantity_used INT NOT NULL COMMENT 'Kasutatud kogus',
    unit_cost DECIMAL(10,2) COMMENT 'Ühiku maksumus kasutamise hetkel',
    total_cost DECIMAL(10,2) COMMENT 'Kogumaksumus',
    
    used_by VARCHAR(255) COMMENT 'Kes kasutas (tehnik, hooldaja)',
    usage_type ENUM('maintenance', 'repair', 'replacement', 'installation', 'other') DEFAULT 'maintenance',
    description TEXT COMMENT 'Kasutamise kirjeldus',
    
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE CASCADE,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE SET NULL,
    FOREIGN KEY (maintenance_request_id) REFERENCES maintenance_requests(id) ON DELETE SET NULL,
    FOREIGN KEY (issue_id) REFERENCES issues(id) ON DELETE SET NULL,
    
    INDEX idx_part (part_id),
    INDEX idx_machine (machine_id),
    INDEX idx_maintenance (maintenance_request_id),
    INDEX idx_issue (issue_id),
    INDEX idx_used_at (used_at),
    INDEX idx_usage_type (usage_type)
);

-- 4. Varuosade tellimuste ajalugu
CREATE TABLE IF NOT EXISTS part_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(100) UNIQUE NOT NULL COMMENT 'Tellimuse number',
    supplier VARCHAR(255) NOT NULL COMMENT 'Tarnija',
    order_date DATE NOT NULL COMMENT 'Tellimuse kuupäev',
    expected_delivery_date DATE COMMENT 'Oodatav tarnekuupäev',
    actual_delivery_date DATE COMMENT 'Tegelik tarnekuupäev',
    
    status ENUM('pending', 'ordered', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    total_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'Tellimuse kogusumma',
    
    ordered_by VARCHAR(255) COMMENT 'Kes tellis',
    notes TEXT COMMENT 'Tellimuse märkused',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_order_number (order_number),
    INDEX idx_supplier (supplier),
    INDEX idx_status (status),
    INDEX idx_order_date (order_date)
);

-- 5. Tellimuse read (millised osad telliti)
CREATE TABLE IF NOT EXISTS part_order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    part_id INT NOT NULL,
    quantity_ordered INT NOT NULL COMMENT 'Tellitud kogus',
    quantity_received INT DEFAULT 0 COMMENT 'Saadud kogus',
    unit_price DECIMAL(10,2) NOT NULL COMMENT 'Ühiku hind',
    total_price DECIMAL(10,2) NOT NULL COMMENT 'Rea kogusumma',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (order_id) REFERENCES part_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE CASCADE,
    
    INDEX idx_order (order_id),
    INDEX idx_part (part_id)
);

-- Näidisandmed varuosade jaoks
INSERT INTO parts (part_number, name, description, category, manufacturer, unit_of_measure, unit_price, quantity_in_stock, minimum_stock_level, maximum_stock_level, reorder_point, supplier, location) VALUES
('PART-001', 'Spindli laager SKF 6205', 'Kuullaager spindli jaoks', 'mechanical', 'SKF', 'tk', 45.50, 8, 5, 20, 7, 'Bearing Solutions OÜ', 'Riiul A-1'),
('PART-002', 'Veorihm HTD 8M-1600', 'Hammasrihm 8mm samm, 1600mm pikkus', 'mechanical', 'Gates', 'tk', 28.90, 3, 5, 15, 6, 'Industrial Belts AS', 'Riiul B-2'),
('PART-003', 'Hüdraulikaõli ISO VG 46', 'Hüdraulikaõli 20L kanister', 'hydraulic', 'Shell', 'l', 8.50, 120, 50, 200, 75, 'Oils & Lubricants OÜ', 'Kemikaalide ladu'),
('PART-004', 'Elektrimootor 3kW', 'Kolmefaasiline elektrimootor 3kW 1450rpm', 'electrical', 'ABB', 'tk', 320.00, 2, 2, 8, 3, 'Electric Motors Ltd', 'Riiul C-1'),
('PART-005', 'Õlifiltri element', 'Hüdraulikaõli filtri element', 'hydraulic', 'Parker', 'tk', 15.75, 12, 8, 25, 10, 'Hydraulic Parts OÜ', 'Riiul A-3'),
('PART-006', 'Pneumaatiline silinder 63x100', 'Pneumaatiline silinder Ø63mm, löök 100mm', 'pneumatic', 'Festo', 'tk', 89.00, 4, 3, 10, 4, 'Pneumatic Solutions AS', 'Riiul D-1'),
('PART-007', 'Lõikeriist CNMG 120408', 'Karbidist lõikeriist treimiseks', 'tooling', 'Sandvik', 'tk', 12.30, 25, 15, 50, 20, 'Cutting Tools OÜ', 'Tööriistade kapp'),
('PART-008', 'Jahutussüsteemi pump', 'Tsirkulatsioonipump jahutussüsteemi jaoks', 'cooling', 'Grundfos', 'tk', 156.00, 1, 2, 6, 2, 'Pump Systems AS', 'Riiul E-2');

-- Seosta mõned varuosad masinatega (näidisandmed)
INSERT INTO machine_parts (machine_id, part_id, quantity_needed, is_critical, replacement_interval_hours, notes) VALUES
(1, 1, 2, TRUE, 2000, 'Spindli laagrid vajavad regulaarset vahetamist'),
(1, 2, 1, TRUE, 1500, 'Peamine veorihm X-telje jaoks'),
(1, 3, 1, FALSE, NULL, 'Hüdraulikaõli täiendamiseks'),
(1, 7, 10, FALSE, 100, 'Lõikeriistad kuluvad kiiresti'),
(2, 1, 1, TRUE, 2500, 'Spindli laager'),
(2, 4, 1, TRUE, 8760, 'Peamine ajamimootor'),
(2, 5, 2, FALSE, 500, 'Õlifiltrid regulaarseks vahetamiseks');
