{"name": "cmms", "version": "1.0.0", "description": "Computerized Maintenance Management System", "main": "server.js", "scripts": {"dev": "bun run --watch server.js", "start": "bun run server.js", "test": "vitest", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "migrate": "bun run database/migrate.js", "seed": "bun run database/seed.js", "setup": "bash scripts/setup.sh", "setup:windows": "scripts\\setup.bat", "build": "echo 'Build step for CSS/JS optimization'", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"bcrypt": "^6.0.0", "ejs": "^3.1.9", "hono": "^3.12.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^7.0.3", "qrcode": "^1.5.3", "sequelize": "^6.37.7"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.10.0", "prettier": "^3.5.3", "vitest": "^1.0.0"}, "type": "module"}