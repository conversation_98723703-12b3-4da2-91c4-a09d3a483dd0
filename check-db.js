import { pool } from './src/config/database.js';

async function checkDatabase() {
  try {
    console.log('Checking database tables...');
    
    // Check if maintenance_requests table exists
    const [tables] = await pool.execute("SHOW TABLES LIKE 'maintenance_requests'");
    console.log('Tables matching maintenance_requests:', tables);
    
    if (tables.length > 0) {
      // Check table structure
      const [structure] = await pool.execute('DESCRIBE maintenance_requests');
      console.log('Table structure:');
      structure.forEach(column => {
        console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${column.Key} ${column.Default || ''}`);
      });
    } else {
      console.log('maintenance_requests table does not exist');
      
      // Check what tables do exist
      const [allTables] = await pool.execute('SHOW TABLES');
      console.log('Available tables:', allTables.map(t => Object.values(t)[0]));
    }
    
  } catch (error) {
    console.error('Database check error:', error.message);
  } finally {
    process.exit();
  }
}

checkDatabase();
