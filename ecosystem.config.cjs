module.exports = {
  apps: [{
    name: 'cmms',
    script: 'server.js',
    interpreter: '/root/.bun/bin/bun',
    cwd: '/var/www/cmms',
    env: {
      NODE_ENV: 'production',
      PORT: 8080,
      PATH: '/root/.bun/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin',
      ALLOW_EXTERNAL_ACCESS: 'true',
      NOTIFICATIONS_ENABLED: 'true'
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: '/var/log/pm2/cmms-error.log',
    out_file: '/var/log/pm2/cmms-out.log',
    log_file: '/var/log/pm2/cmms.log',
    time: true
  }]
};
