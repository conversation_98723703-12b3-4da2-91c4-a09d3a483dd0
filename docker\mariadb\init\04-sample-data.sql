-- Sample data for CMMS database
USE cmms_db;

-- Insert sample operators
INSERT IGNORE INTO operators (operator_number, name, email, department) VALUES
('OP001', 'Mart Tamm', '<EMAIL>', 'Tootmine'),
('OP002', '<PERSON>', '<EMAIL>', 'Tootmine'),
('OP003', '<PERSON><PERSON>', '<EMAIL>', '<PERSON>old<PERSON>'),
('OP004', '<PERSON><PERSON>', '<EMAIL>', 'Kvaliteedikontroll'),
('OP005', 'Toomas Toom', '<EMAIL>', 'Tootmine');

-- Insert sample machine groups
INSERT IGNORE INTO machine_groups (name, description, department, supervisor) VALUES
('Lõikemasinad', 'CNC lõikemasinad ja tööriistamasinad', 'Tootmine', 'Mart Tamm'),
('Pressid', 'Hüdraulilised ja pneumaatilised pressid', 'Tootmine', '<PERSON>'),
('Kon<PERSON>ierid', 'Transpordi- ja sorteerimissüsteemid', 'Logisti<PERSON>', '<PERSON><PERSON>gi'),
('Kompressorid', 'Õhukompressorid ja pneumaatikasüsteemid', 'Tehnosüsteemid', 'Liis Lepik');

-- Insert sample machines
INSERT IGNORE INTO machines (machine_number, name, manufacturer, model, serial_number, manufacturing_year, department, responsible_operator, location, status, group_id) VALUES
('M001', 'CNC Lõikemasin 1', 'Haas', 'VF-2', 'HAS123456', 2020, 'Tootmine', 'Mart Tamm', 'Tsehh A, Sektsioon 1', 'online', 1),
('M002', 'Hüdrauliline Press', 'Schuler', 'MSP-500', 'SCH789012', 2019, 'Tootmine', 'Karin Kask', 'Tsehh B, Sektsioon 2', 'online', 2),
('M003', 'Konveier Liin 1', 'Siemens', 'CV-1000', 'SIE345678', 2021, 'Logistika', 'Jaan Jõgi', 'Ladu, Sektsioon 1', 'online', 3),
('M004', 'Õhukompressor', 'Atlas Copco', 'GA-75', 'ATC901234', 2018, 'Tehnosüsteemid', 'Liis Lepik', 'Tehnohoone', 'maintenance', 4),
('M005', 'CNC Lõikemasin 2', 'Haas', 'VF-3', 'HAS567890', 2022, 'Tootmine', 'Toomas Toom', 'Tsehh A, Sektsioon 2', 'online', 1);

-- Insert sample parts
INSERT IGNORE INTO parts (part_number, name, description, manufacturer, category, location, quantity_in_stock, minimum_stock, unit_price, supplier, is_critical) VALUES
('P001', 'Hüdrauliõli ISO 46', 'Hüdrauliõli ISO VG 46, 20L', 'Shell', 'Õlid ja määrded', 'Ladu A1', 15, 5, 45.50, 'Shell Eesti', TRUE),
('P002', 'Laager 6205-2RS', 'Kuullaager 6205-2RS, tihendatud', 'SKF', 'Laagrid', 'Ladu B2', 25, 10, 12.30, 'SKF Baltic', TRUE),
('P003', 'V-rihm SPZ 1250', 'V-rihm SPZ profiiliga, pikkus 1250mm', 'Gates', 'Rihmaülekanded', 'Ladu C3', 8, 3, 18.75, 'Gates Eesti', FALSE),
('P004', 'Pneumaatiline silinder', 'Pneumaatiline silinder 63x100mm', 'Festo', 'Pneumaatika', 'Ladu D4', 5, 2, 125.00, 'Festo Baltic', TRUE),
('P005', 'Elektrimootor 3kW', 'Kolmefaasiline elektrimootor 3kW, 1450 p/min', 'ABB', 'Elektrimootorid', 'Ladu E5', 3, 1, 450.00, 'ABB Eesti', TRUE);

-- Insert sample maintenance partners
INSERT IGNORE INTO maintenance_partners (company_name, contact_person, email, phone, address, specializations, hourly_rate, notes) VALUES
('TehnoHooldus OÜ', 'Mart Tamm', '<EMAIL>', '+372 5123 4567', 'Tallinn, Mustamäe tee 16', '["mechanical", "electrical", "hydraulic"]', 45.00, 'Spetsialiseerub tööstusseadmete hooldusele'),
('Elektripartner AS', 'Karin Kask', '<EMAIL>', '+372 5234 5678', 'Tartu, Riia 185', '["electrical", "automation", "calibration"]', 55.00, 'Elektrisüsteemide ja automaatika ekspert'),
('MehaanikaPro OÜ', 'Jaan Jõgi', '<EMAIL>', '+372 5345 6789', 'Pärnu, Papiniidu 8', '["mechanical", "pneumatic", "cleaning"]', 40.00, 'Mehhaaniliste süsteemide hooldus ja remont'),
('KvaliteetKontroll OÜ', 'Liis Lepik', '<EMAIL>', '+372 5456 7890', 'Narva, Kreenholmi 1', '["inspection", "calibration", "testing"]', 60.00, 'Seadmete kontrollimine ja kalibreerimine'),
('KiireRemont 24/7', 'Toomas Toom', '<EMAIL>', '+372 5567 8901', 'Tallinn, Peterburi tee 90', '["emergency", "mechanical", "electrical"]', 75.00, '24/7 hädahoolduse teenus');

-- Insert sample maintenance requests
INSERT IGNORE INTO maintenance_requests (machine_id, operator_number, operator_name, maintenance_type, urgency, title, description, status, requested_date, scheduled_date, assigned_to, partner_id) VALUES
(1, 'OP001', 'Mart Tamm', 'preventive', 'medium', 'CNC masina regulaarne hooldus', 'Igakuine ennetav hooldus - õlide vahetamine, filtrite kontroll', 'scheduled', '2024-01-15', '2024-01-20', 'Jaan Jõgi', 1),
(2, 'OP002', 'Karin Kask', 'corrective', 'high', 'Hüdraulilise pressi rõhu probleem', 'Press ei saavuta täisrõhku, võimalik leke hüdraulikasüsteemis', 'in_progress', '2024-01-16', '2024-01-17', 'Liis Lepik', 1),
(3, 'OP003', 'Jaan Jõgi', 'inspection', 'low', 'Konveieri ohutuskontroll', 'Kvartaline ohutuse ja töökindluse kontroll', 'requested', '2024-01-18', NULL, NULL, 4),
(4, 'OP004', 'Liis Lepik', 'emergency', 'urgent', 'Kompressori rike', 'Kompressor ei käivitu, tootmine seisab', 'completed', '2024-01-10', '2024-01-10', 'Toomas Toom', 5),
(5, 'OP005', 'Toomas Toom', 'calibration', 'medium', 'CNC masina kalibreerimine', 'Täpsuse kontroll ja kalibreerimine pärast remonti', 'scheduled', '2024-01-19', '2024-01-22', 'Mart Tamm', 2);

-- Insert sample issues
INSERT IGNORE INTO issues (machine_id, operator_number, issue_type, issue_category, severity, title, description, status, assigned_to) VALUES
(1, 'OP001', 'mechanical', 'issue', 'medium', 'Ebatavaline müra lõikamisel', 'CNC masin tekitab ebatavalist müra Y-telje liikumisel', 'new', 'Jaan Jõgi'),
(2, 'OP002', 'hydraulic', 'issue', 'high', 'Õlileke pressi all', 'Märgatud õlileket pressi hüdraulikasüsteemis', 'in_progress', 'Liis Lepik'),
(3, 'OP003', 'electrical', 'issue', 'low', 'Konveieri LED valgustus vilgub', 'Üks LED lamp vilgub, võib varsti kustuda', 'new', NULL),
(4, 'OP004', 'mechanical', 'maintenance', 'medium', 'Kompressori filter vajab vahetamist', 'Õhufilter on määrdunud ja vajab vahetamist', 'completed', 'Toomas Toom'),
(5, 'OP005', 'software', 'issue', 'high', 'CNC programmi viga', 'G-koodi programm annab veateate, ei saa tööd alustada', 'waiting', 'Mart Tamm');

-- Insert sample development projects
INSERT IGNORE INTO development_projects (title, description, initiator, start_date, end_date, status, progress_percentage) VALUES
('Tootmisliini automatiseerimine', 'Konveierisüsteemi täielik automatiseerimine ja integreerimine ERP süsteemiga', 'Jaan Jõgi', '2024-01-01', '2024-06-30', 'active', 25),
('Ennetava hoolduse süsteem', 'IoT sensorite paigaldamine masinate jälgimiseks ja ennetava hoolduse rakendamine', 'Liis Lepik', '2024-02-01', '2024-08-31', 'active', 15),
('Energiatõhususe projekt', 'Elektrimootorite väljavahetamine energiatõhusamate vastu', 'Mart Tamm', '2023-10-01', '2024-03-31', 'active', 75);

-- Link machines to projects
INSERT IGNORE INTO project_machines (project_id, machine_id) VALUES
(1, 3), -- Konveier automatiseerimisprojektis
(2, 1), (2, 2), (2, 4), (2, 5), -- Kõik masinad ennetava hoolduse projektis
(3, 1), (3, 5); -- CNC masinad energiatõhususe projektis
