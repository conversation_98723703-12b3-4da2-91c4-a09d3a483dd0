import { pool } from '../config/database.js';

export class Notification {
  static async create(notificationData) {
    try {
      const {
        from_user_id,
        to_user_id,
        title,
        message,
        type = 'general',
        machine_id = null
      } = notificationData;

      const [result] = await pool.execute(
        `INSERT INTO notifications (
          from_user_id, to_user_id, title, message, type, machine_id
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [from_user_id, to_user_id, title, message, type, machine_id]
      );

      return await this.findById(result.insertId);
    } catch (error) {
      throw new Error(`Failed to create notification: ${error.message}`);
    }
  }

  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT 
          n.*,
          fu.username as from_username,
          fu.full_name as from_full_name,
          tu.username as to_username,
          tu.full_name as to_full_name,
          m.machine_number,
          m.name as machine_name
         FROM notifications n
         LEFT JOIN users fu ON n.from_user_id = fu.id
         LEFT JOIN users tu ON n.to_user_id = tu.id
         LEFT JOIN machines m ON n.machine_id = m.id
         WHERE n.id = ?`,
        [id]
      );

      return rows[0] || null;
    } catch (error) {
      throw new Error(`Failed to find notification: ${error.message}`);
    }
  }

  static async findByUserId(userId, options = {}) {
    try {
      const { limit = 50, unreadOnly = false, includeRead = true } = options;
      
      let query = `
        SELECT 
          n.*,
          fu.username as from_username,
          fu.full_name as from_full_name,
          m.machine_number,
          m.name as machine_name
        FROM notifications n
        LEFT JOIN users fu ON n.from_user_id = fu.id
        LEFT JOIN machines m ON n.machine_id = m.id
        WHERE n.to_user_id = ?
      `;
      
      const params = [userId];

      if (unreadOnly) {
        query += ' AND n.is_read = FALSE';
      }

      query += ' ORDER BY n.created_at DESC';
      
      if (limit) {
        query += ` LIMIT ${parseInt(limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to find notifications for user: ${error.message}`);
    }
  }

  static async markAsRead(id, userId) {
    try {
      const [result] = await pool.execute(
        `UPDATE notifications 
         SET is_read = TRUE, read_at = CURRENT_TIMESTAMP 
         WHERE id = ? AND to_user_id = ?`,
        [id, userId]
      );

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to mark notification as read: ${error.message}`);
    }
  }

  static async markAllAsRead(userId) {
    try {
      const [result] = await pool.execute(
        `UPDATE notifications 
         SET is_read = TRUE, read_at = CURRENT_TIMESTAMP 
         WHERE to_user_id = ? AND is_read = FALSE`,
        [userId]
      );

      return result.affectedRows;
    } catch (error) {
      throw new Error(`Failed to mark all notifications as read: ${error.message}`);
    }
  }

  static async getUnreadCount(userId) {
    try {
      const [rows] = await pool.execute(
        `SELECT COUNT(*) as count FROM notifications 
         WHERE to_user_id = ? AND is_read = FALSE`,
        [userId]
      );

      return rows[0]?.count || 0;
    } catch (error) {
      throw new Error(`Failed to get unread count: ${error.message}`);
    }
  }

  static async delete(id, userId) {
    try {
      const [result] = await pool.execute(
        'DELETE FROM notifications WHERE id = ? AND to_user_id = ?',
        [id, userId]
      );

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete notification: ${error.message}`);
    }
  }

  // Send notification to all users with specific roles
  static async sendToRoles(fromUserId, roles, title, message, type = 'general', machineId = null) {
    try {
      // Get all users with specified roles
      const rolesList = Array.isArray(roles) ? roles : [roles];
      const placeholders = rolesList.map(() => '?').join(',');
      
      const [users] = await pool.execute(
        `SELECT id FROM users WHERE role IN (${placeholders})`,
        rolesList
      );

      const notifications = [];
      for (const user of users) {
        // Skip sending to self only if fromUserId is not null
        if (fromUserId && user.id === fromUserId) {
          continue;
        }
        
        const notification = await this.create({
          from_user_id: fromUserId,
          to_user_id: user.id,
          title,
          message,
          type,
          machine_id: machineId
        });
        notifications.push(notification);
      }

      return notifications;
    } catch (error) {
      throw new Error(`Failed to send notifications to roles: ${error.message}`);
    }
  }

  // Send notification to specific user
  static async sendToUser(fromUserId, toUserId, title, message, type = 'general', machineId = null) {
    try {
      return await this.create({
        from_user_id: fromUserId,
        to_user_id: toUserId,
        title,
        message,
        type,
        machine_id: machineId
      });
    } catch (error) {
      throw new Error(`Failed to send notification to user: ${error.message}`);
    }
  }

  // Get all notifications (for admin view)
  static async findAll(options = {}) {
    try {
      const { limit = 100, type = null, unreadOnly = false } = options;
      
      let query = `
        SELECT 
          n.*,
          fu.username as from_username,
          fu.full_name as from_full_name,
          tu.username as to_username,
          tu.full_name as to_full_name,
          m.machine_number,
          m.name as machine_name
        FROM notifications n
        LEFT JOIN users fu ON n.from_user_id = fu.id
        LEFT JOIN users tu ON n.to_user_id = tu.id
        LEFT JOIN machines m ON n.machine_id = m.id
        WHERE 1=1
      `;
      
      const params = [];

      if (type) {
        query += ' AND n.type = ?';
        params.push(type);
      }

      if (unreadOnly) {
        query += ' AND n.is_read = FALSE';
      }

      query += ' ORDER BY n.created_at DESC';
      
      if (limit) {
        query += ` LIMIT ${parseInt(limit)}`;
      }

      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      throw new Error(`Failed to find all notifications: ${error.message}`);
    }
  }

  // Get notification statistics
  static async getStats() {
    try {
      const [rows] = await pool.execute(`
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN is_read = FALSE THEN 1 ELSE 0 END) as unread,
          SUM(CASE WHEN type = 'urgent' THEN 1 ELSE 0 END) as urgent,
          SUM(CASE WHEN type = 'maintenance' THEN 1 ELSE 0 END) as maintenance,
          SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as today
        FROM notifications
      `);

      return rows[0] || {
        total: 0,
        unread: 0,
        urgent: 0,
        maintenance: 0,
        today: 0
      };
    } catch (error) {
      throw new Error(`Failed to get notification stats: ${error.message}`);
    }
  }

  // Delete notification (admin only)
  static async delete(id) {
    try {
      const [result] = await pool.execute(
        'DELETE FROM notifications WHERE id = ?',
        [id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      throw new Error(`Failed to delete notification: ${error.message}`);
    }
  }
}
