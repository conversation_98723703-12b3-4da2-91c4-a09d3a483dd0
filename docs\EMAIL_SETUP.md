# 📧 CMMS Email Notifications Setup Guide

Complete guide for setting up email notifications in your CMMS system, especially for Hetzner VPS deployments.

## 🚨 **Important: Hetzner SMTP Port Restrictions**

<PERSON><PERSON><PERSON> blocks outbound SMTP ports by default to prevent spam:

- **Port 25:** ❌ Blocked (standard SMTP)
- **Port 587:** ❌ Initially blocked (submission)
- **Port 465:** ❌ Initially blocked (secure SMTP)

### 🔓 **Solutions for Hetz<PERSON>**

#### **Option 1: Request Port Unblocking (Recommended for Self-Hosted)**
1. **Contact Hetzner Support** via their console
2. **Explain use case:** "Need SMTP for application notifications"
3. **Provide details:** Server IP and intended use
4. **Usually approved within 24-48 hours**

#### **Option 2: Use External SMTP Service (Recommended)**
Use third-party SMTP services that work reliably from any server.

## 📮 **Recommended SMTP Services**

### **1. Gmail SMTP (Free & Easy)**

**Pros:** Free, reliable, easy setup
**Cons:** Daily sending limits, requires Google account

**Setup:**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_gmail_app_password
FROM_EMAIL=CMMS System <<EMAIL>>
```

**Steps:**
1. Enable 2FA on your Gmail account
2. Go to Google Account → Security → App passwords
3. Generate an app password for "Mail"
4. Use the 16-character app password in `.env`

### **2. SendGrid (Professional)**

**Pros:** 100 emails/day free, excellent deliverability, professional
**Cons:** Requires account setup

**Setup:**
```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=apikey
SMTP_PASS=your_sendgrid_api_key
FROM_EMAIL=CMMS System <<EMAIL>>
```

**Steps:**
1. Sign up at [SendGrid](https://sendgrid.com/)
2. Verify your sender identity
3. Create an API key with "Mail Send" permissions
4. Use "apikey" as username and API key as password

### **3. Mailgun (Developer-Friendly)**

**Pros:** Developer-focused, good documentation, flexible
**Cons:** Requires domain verification for production

**Setup:**
```env
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_mailgun_password
FROM_EMAIL=CMMS System <<EMAIL>>
```

### **4. Amazon SES (Scalable)**

**Pros:** Very cheap, highly scalable, AWS integration
**Cons:** More complex setup, requires AWS account

**Setup:**
```env
SMTP_HOST=email-smtp.eu-west-1.amazonaws.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_ses_access_key
SMTP_PASS=your_ses_secret_key
FROM_EMAIL=CMMS System <<EMAIL>>
```

## 🔧 **CMMS Email Configuration**

### **1. Environment Variables**

Edit your `.env` file:

```env
# Enable notifications
NOTIFICATIONS_ENABLED=true

# SMTP Configuration (choose one from above)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=CMMS System <<EMAIL>>

# Notification Recipients
ADMIN_EMAIL=<EMAIL>
MAINTENANCE_TEAM_EMAIL=<EMAIL>

# Notification Types
CRITICAL_ISSUE_NOTIFICATIONS=true
MAINTENANCE_REMINDERS=true
```

### **2. Test Email Configuration**

Run the email test script:

```bash
# Test with default admin email
bun scripts/test-email.js

# Test with specific email
bun scripts/test-email.js <EMAIL>
```

### **3. Notification Types**

Your CMMS sends emails for:

- **🚨 Critical Issues:** Immediate alerts for urgent problems
- **🔧 Maintenance Requests:** New maintenance requests
- **📅 Maintenance Reminders:** Scheduled maintenance alerts
- **👥 Partner Assignments:** External partner notifications
- **📊 System Alerts:** Low inventory, overdue tasks

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **Authentication Failed (EAUTH)**
```
❌ Error: Invalid login: 535 Authentication failed
```

**Solutions:**
- **Gmail:** Use App Password, not regular password
- **SendGrid:** Use "apikey" as username
- **Check credentials:** Verify username/password are correct

#### **Connection Timeout (ETIMEDOUT)**
```
❌ Error: Connection timeout
```

**Solutions:**
- **Hetzner:** Request SMTP port unblocking
- **Firewall:** Check server firewall settings
- **Try different port:** Use port 465 with SMTP_SECURE=true

#### **Message Rejected**
```
❌ Error: Message rejected
```

**Solutions:**
- **FROM_EMAIL:** Use verified sender address
- **Domain verification:** Complete sender verification
- **Content:** Avoid spam-like content

### **Testing Commands**

```bash
# Test SMTP connection
telnet smtp.gmail.com 587

# Check if ports are open
nmap -p 25,587,465 smtp.gmail.com

# Test with curl
curl -v telnet://smtp.gmail.com:587
```

## 🔒 **Security Best Practices**

### **1. Use App Passwords**
- Never use your main email password
- Generate app-specific passwords
- Rotate passwords regularly

### **2. Secure Environment Variables**
```bash
# Set proper file permissions
chmod 600 .env

# Never commit .env to git
echo ".env" >> .gitignore
```

### **3. Monitor Email Usage**
- Set up usage alerts
- Monitor bounce rates
- Track delivery statistics

## 📊 **Email Service Comparison**

| Service | Free Tier | Setup Difficulty | Deliverability | Best For |
|---------|-----------|------------------|----------------|----------|
| Gmail | Unlimited* | Easy | Good | Development/Small teams |
| SendGrid | 100/day | Medium | Excellent | Production |
| Mailgun | 5,000/month | Medium | Excellent | Developers |
| Amazon SES | 200/day | Hard | Excellent | Enterprise |

*Subject to Gmail's sending limits

## 🎯 **Recommended Setup for Production**

### **Small Teams (< 10 users)**
- **Service:** Gmail SMTP
- **Cost:** Free
- **Setup time:** 5 minutes

### **Medium Teams (10-50 users)**
- **Service:** SendGrid
- **Cost:** Free (100 emails/day)
- **Setup time:** 15 minutes

### **Large Organizations (50+ users)**
- **Service:** Amazon SES or Mailgun
- **Cost:** $1-5/month
- **Setup time:** 30-60 minutes

## 🚀 **Quick Start**

1. **Choose SMTP service** (Gmail for quick start)
2. **Update .env file** with SMTP settings
3. **Test configuration:** `bun scripts/test-email.js`
4. **Verify notifications work** by creating a test issue
5. **Monitor email delivery** in your service dashboard

## 📞 **Support**

- **CMMS Issues:** [GitHub Issues](https://github.com/JoonasMagi/CMMS/issues)
- **Hetzner SMTP:** [Hetzner Support](https://docs.hetzner.com/)
- **Gmail Setup:** [Google Support](https://support.google.com/accounts/answer/185833)
- **SendGrid:** [SendGrid Docs](https://docs.sendgrid.com/)

Your CMMS email notifications will keep your team informed and ensure critical issues are addressed promptly! 📧✨
