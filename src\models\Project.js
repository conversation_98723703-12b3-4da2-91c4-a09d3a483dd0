import { pool } from '../config/database.js';

class Project {
  constructor(data) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description;
    this.initiator = data.initiator;
    this.start_date = data.start_date;
    this.end_date = data.end_date;
    this.status = data.status;
    this.progress_percentage = data.progress_percentage;
    this.tags = data.tags;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Get all projects
  static async findAll(filters = {}) {
    try {
      let query = `
        SELECT dp.*,
               COUNT(pm.machine_id) as machine_count
        FROM development_projects dp
        LEFT JOIN project_machines pm ON dp.id = pm.project_id
      `;

      const conditions = [];
      const params = [];

      if (filters.status) {
        conditions.push('dp.status = ?');
        params.push(filters.status);
      }

      if (filters.search) {
        conditions.push('(dp.title LIKE ? OR dp.description LIKE ? OR dp.initiator LIKE ?)');
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' GROUP BY dp.id ORDER BY dp.created_at DESC';

      const [rows] = await pool.execute(query, params);
      return rows.map(row => new Project(row));
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  }

  // Get project by ID
  static async findById(id) {
    try {
      const [rows] = await pool.execute(
        `SELECT dp.*,
                COUNT(pm.machine_id) as machine_count
         FROM development_projects dp
         LEFT JOIN project_machines pm ON dp.id = pm.project_id
         WHERE dp.id = ?
         GROUP BY dp.id`,
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      return new Project(rows[0]);
    } catch (error) {
      console.error('Error fetching project by ID:', error);
      throw error;
    }
  }

  // Get project with associated machines and documents
  static async findByIdWithMachines(id) {
    try {
      const project = await this.findById(id);
      if (!project) {
        return null;
      }

      // Get associated machines
      const [machineRows] = await pool.execute(
        `SELECT m.id, m.machine_number, m.name, m.location, m.status
         FROM machines m
         INNER JOIN project_machines pm ON m.id = pm.machine_id
         WHERE pm.project_id = ?
         ORDER BY m.machine_number`,
        [id]
      );

      // Get associated documents
      const [documentRows] = await pool.execute(
        `SELECT d.id, d.title, d.description, d.document_type, d.file_name,
                d.file_size, d.mime_type, d.created_at, d.uploaded_by
         FROM documents d
         WHERE d.project_id = ? AND d.is_active = TRUE
         ORDER BY d.document_type, d.created_at DESC`,
        [id]
      );

      project.machines = machineRows;
      project.documents = documentRows;
      return project;
    } catch (error) {
      console.error('Error fetching project with machines and documents:', error);
      throw error;
    }
  }

  // Create new project
  static async create(projectData) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      const { title, description, initiator, start_date, end_date, machines = [] } = projectData;

      // Validate required fields
      if (!title) {
        throw new Error('Project title is required');
      }

      // Insert project
      const [result] = await connection.execute(
        `INSERT INTO development_projects (title, description, initiator, start_date, end_date, status, progress_percentage)
         VALUES (?, ?, ?, ?, ?, 'active', 0)`,
        [title, description || null, initiator || null, start_date || null, end_date || null]
      );

      const projectId = result.insertId;

      // Associate machines with project
      if (machines.length > 0) {
        const machineValues = machines.map(machineId => [projectId, parseInt(machineId)]);
        await connection.execute(
          `INSERT INTO project_machines (project_id, machine_id) VALUES ${machineValues.map(() => '(?, ?)').join(', ')}`,
          machineValues.flat()
        );
      }

      await connection.commit();
      return await this.findByIdWithMachines(projectId);
    } catch (error) {
      await connection.rollback();
      console.error('Error creating project:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // Update project
  static async update(id, projectData) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      const {
        title,
        description,
        initiator,
        start_date,
        end_date,
        status,
        progress_percentage,
        machines,
      } = projectData;

      // Update project basic info
      await connection.execute(
        `UPDATE development_projects
         SET title = ?, description = ?, initiator = ?, start_date = ?, end_date = ?,
             status = ?, progress_percentage = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [
          title,
          description || null,
          initiator || null,
          start_date || null,
          end_date || null,
          status || 'active',
          progress_percentage || 0,
          id,
        ]
      );

      // Update machine associations if provided
      if (machines !== undefined) {
        // Remove existing associations
        await connection.execute('DELETE FROM project_machines WHERE project_id = ?', [id]);

        // Add new associations
        if (machines.length > 0) {
          const machineValues = machines.map(machineId => [id, parseInt(machineId)]);
          await connection.execute(
            `INSERT INTO project_machines (project_id, machine_id) VALUES ${machineValues.map(() => '(?, ?)').join(', ')}`,
            machineValues.flat()
          );
        }
      }

      await connection.commit();
      return await this.findByIdWithMachines(id);
    } catch (error) {
      await connection.rollback();
      console.error('Error updating project:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // Update only progress
  static async updateProgress(id, progressData) {
    try {
      const { progress_percentage, notes } = progressData;

      await pool.execute(
        `UPDATE development_projects
         SET progress_percentage = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [progress_percentage || 0, id]
      );

      // If notes provided, you might want to store them in a separate notes/comments table
      // For now, we'll just update the project

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating project progress:', error);
      throw error;
    }
  }

  // Delete project
  static async delete(id) {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // Delete machine associations first (due to foreign key constraints)
      await connection.execute('DELETE FROM project_machines WHERE project_id = ?', [id]);

      // Delete project
      await connection.execute('DELETE FROM development_projects WHERE id = ?', [id]);

      await connection.commit();
      return true;
    } catch (error) {
      await connection.rollback();
      console.error('Error deleting project:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // Get project statistics
  static async getStatistics() {
    try {
      const [stats] = await pool.execute(`
        SELECT
          COUNT(*) as total_projects,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_projects,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_projects,
          AVG(progress_percentage) as average_progress
        FROM projects
      `);

      return stats[0];
    } catch (error) {
      console.error('Error fetching project statistics:', error);
      throw error;
    }
  }

  // Calendar-specific methods for displaying projects in calendar
  static async getCalendarEvents(startDate, endDate) {
    try {
      const [rows] = await pool.execute(`
        SELECT dp.*
        FROM development_projects dp
        WHERE (
          (dp.start_date IS NOT NULL AND dp.start_date BETWEEN ? AND ?)
          OR (dp.end_date IS NOT NULL AND dp.end_date BETWEEN ? AND ?)
          OR (dp.start_date <= ? AND dp.end_date >= ?)
        )
        AND dp.status IN ('planning', 'in_progress', 'testing', 'on_hold')
        ORDER BY dp.start_date ASC, dp.status DESC
      `, [startDate, endDate, startDate, endDate, startDate, endDate]);

      return rows.map(project => ({
        id: project.id,
        title: `Projekt: ${project.title}`,
        type: 'project',
        start: project.start_date,
        end: project.end_date || project.start_date,
        description: project.description,
        initiator: project.initiator,
        status: project.status,
        progress_percentage: project.progress_percentage,
        tags: project.tags,
        created_at: project.created_at,
        color: this.getEventColor(project.status, project.progress_percentage)
      }));
    } catch (error) {
      throw new Error(`Failed to get project calendar events: ${error.message}`);
    }
  }

  static getEventColor(status, progress) {
    // Status-based colors for projects
    switch (status) {
      case 'completed': return '#10b981'; // green
      case 'in_progress': return '#3b82f6'; // blue
      case 'testing': return '#8b5cf6'; // purple
      case 'on_hold': return '#f59e0b'; // amber
      case 'cancelled': return '#6b7280'; // gray
      case 'planning': return '#06b6d4'; // cyan
      default: return '#6b7280'; // gray
    }
  }

  static async getCalendarStats(startDate, endDate) {
    try {
      const [rows] = await pool.execute(`
        SELECT
          COUNT(*) as total,
          SUM(CASE WHEN status = 'planning' THEN 1 ELSE 0 END) as planning,
          SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
          SUM(CASE WHEN status = 'testing' THEN 1 ELSE 0 END) as testing,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
          SUM(CASE WHEN status = 'on_hold' THEN 1 ELSE 0 END) as on_hold,
          AVG(progress_percentage) as avg_progress
        FROM development_projects dp
        WHERE (
          (dp.start_date IS NOT NULL AND dp.start_date BETWEEN ? AND ?)
          OR (dp.end_date IS NOT NULL AND dp.end_date BETWEEN ? AND ?)
          OR (dp.start_date <= ? AND dp.end_date >= ?)
        )
      `, [startDate, endDate, startDate, endDate, startDate, endDate]);

      return rows[0] || {
        total: 0, planning: 0, in_progress: 0, testing: 0, completed: 0, on_hold: 0, avg_progress: 0
      };
    } catch (error) {
      throw new Error(`Failed to get project calendar stats: ${error.message}`);
    }
  }
}

export default Project;
