-- Create issues table for tracking machine issues (updated to match both schema.sql and Issue.js model)
CREATE TABLE IF NOT EXISTS issues (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    operator_number VARCHAR(50) NOT NULL,
    operator_name VARCHAR(100),
    issue_type ENUM('mechanical', 'electrical', 'hydraulic', 'software', 'other') NOT NULL,
    issue_category ENUM('issue', 'maintenance') NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    severity ENUM('low', 'medium', 'high') DEFAULT 'medium',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    photo_filename VA<PERSON>HAR(255),
    photo_data LONGBLOB,
    photo_mime_type VARCHAR(100),
    photo_size INT,
    status ENUM('new', 'in_progress', 'waiting', 'completed', 'cancelled') DEFAULT 'new',
    assigned_to VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_issues_machine_id ON issues(machine_id);
CREATE INDEX idx_issues_status ON issues(status);
CREATE INDEX idx_issues_severity ON issues(severity);
CREATE INDEX idx_issues_issue_type ON issues(issue_type);
CREATE INDEX idx_issues_created_at ON issues(created_at);

-- Insert sample issues to test the table structure
INSERT INTO issues (machine_id, operator_number, operator_name, issue_type, issue_category, priority, severity, title, description, status, assigned_to) VALUES
(1, 'OP-001', 'Jaan Tamm', 'mechanical', 'issue', 'high', 'high', 'CNC freespingi vibratsioon', 'Masin vibreerib liiga palju töötamise ajal, võimalik laagri probleem', 'new', 'Jaan Tamm'),
(2, 'OP-002', 'Mari Kask', 'electrical', 'issue', 'medium', 'medium', 'Keevitusposte elektriprobleem', 'Keevituspost ei käivitu korralikult, kontrollida elektriühendusi', 'in_progress', 'Mari Kask'),
(3, 'OP-003', 'Peeter Saar', 'hydraulic', 'issue', 'high', 'high', 'Hüdraulilise pressi rõhulangus', 'Press ei suuda saavutada täit rõhku, võimalik lekkimise koht', 'new', 'Peeter Saar'),
(4, 'OP-004', 'Anna Mets', 'software', 'maintenance', 'low', 'low', 'Lõikepingi tarkvara uuendus', 'Plaanitud tarkvara uuendus uuema versiooni peale', 'new', 'Anna Mets'),
(5, 'OP-005', 'Toomas Kivi', 'other', 'maintenance', 'medium', 'medium', 'Kompressori filter', 'Kompressori õhufilter vajab vahetamist', 'completed', 'Toomas Kivi')
ON DUPLICATE KEY UPDATE title = title;
