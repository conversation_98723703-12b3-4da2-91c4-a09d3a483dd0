// Standard viewport meta tag for consistent mobile display
export function getViewportMeta() {
  return `<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">`;
}

// CSS for consistent mobile display across different devices and browsers
export function getMobileCSS() {
  return `
    <style>
      /* Ensure consistent mobile display */
      html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }

      body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      /* Prevent zoom on input focus on iOS */
      input[type="text"],
      input[type="email"],
      input[type="password"],
      input[type="number"],
      input[type="tel"],
      input[type="url"],
      input[type="search"],
      textarea,
      select {
        font-size: 16px !important;
      }

      /* Consistent touch targets */
      button,
      input[type="submit"],
      input[type="button"],
      a {
        min-height: 44px;
        min-width: 44px;
      }

      /* Remove tap highlight on mobile */
      * {
        -webkit-tap-highlight-color: transparent;
      }

      /* Ensure proper scaling */
      @media screen and (max-width: 768px) {
        .container {
          padding-left: 1rem;
          padding-right: 1rem;
        }
      }
    </style>
  `;
}

// Standard navigation component for all CMMS views
export function getNavigationHTML(currentPage = '', pageTitle = 'CMMS', userRole = 'viewer') {
  // Define main navigation items (kompaktne versioon)
  const mainNavItems = [
    { href: '/admin', icon: 'fas fa-home', label: 'Avaleht', key: 'dashboard', roles: ['admin'] },
    { href: '/maintenance', icon: 'fas fa-home', label: 'Avaleht', key: 'maintenance-dashboard', roles: ['maintenance'] },
    { href: '/operator', icon: 'fas fa-home', label: 'Avaleht', key: 'operator', roles: ['operator'] },
    { href: '/viewer', icon: 'fas fa-home', label: 'Avaleht', key: 'viewer', roles: ['viewer'] },
    { href: '/machines', icon: 'fas fa-cogs', label: 'Masinad', key: 'machines', roles: ['admin', 'maintenance', 'operator', 'viewer'] },
    { href: '/projects', icon: 'fas fa-project-diagram', label: 'Projektid', key: 'projects', roles: ['admin', 'maintenance', 'viewer', 'operator'] },
    { href: '/projects/new', icon: 'fas fa-plus', label: 'Uus projekt', key: 'new-project', roles: ['operator'] },
    { href: '/admin/issues', icon: 'fas fa-exclamation-triangle', label: 'Rikked', key: 'issues', roles: ['admin', 'maintenance'] },
    { href: '/admin/maintenance', icon: 'fas fa-wrench', label: 'Hooldus', key: 'maintenance-page', roles: ['admin', 'maintenance'] },
    { href: '/admin/parts', icon: 'fas fa-boxes', label: 'Varuosad', key: 'parts', roles: ['admin', 'maintenance'] },
    { href: '/admin/reports', icon: 'fas fa-chart-bar', label: 'Aruanded', key: 'reports', roles: ['admin', 'viewer', 'maintenance'] },
  ];

  // Admin-only dropdown items
  const adminDropdownItems = [
    { href: '/admin/machine-groups', icon: 'fas fa-layer-group', label: 'Masinate grupid', key: 'groups' },
    { href: '/admin/partners', icon: 'fas fa-handshake', label: 'Partnerid', key: 'partners' },
    { href: '/admin/documents', icon: 'fas fa-file-alt', label: 'Dokumendid', key: 'documents' },
    { href: '/admin/users', icon: 'fas fa-users', label: 'Kasutajad', key: 'users' },
  ];

  // Filter navigation items based on user role
  const navItems = mainNavItems.filter(item => item.roles.includes(userRole));
  const showAdminDropdown = userRole === 'admin';

  const getNavClass = key => {
    return currentPage === key
      ? 'bg-blue-800 px-2 py-2 rounded min-h-[44px] flex items-center text-sm'
      : 'hover:bg-blue-700 px-2 py-2 rounded min-h-[44px] flex items-center text-sm';
  };

  const getMobileNavClass = key => {
    return currentPage === key
      ? 'block bg-blue-800 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center'
      : 'block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center';
  };

  return `
    <!-- Navigation -->
    <nav class="bg-blue-600 text-white">
      <div class="container mx-auto px-4">
        <!-- Desktop Navigation -->
        <div class="hidden md:flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold">${pageTitle}</h1>
          </div>
          <div class="flex space-x-1">
            ${navItems
              .map(
                item => `
              <a href="${item.href}" class="${getNavClass(item.key)}" title="${item.label}">
                <i class="${item.icon}"></i>
                <span class="ml-1 hidden lg:inline">${item.label}</span>
              </a>
            `
              )
              .join('')}
            ${showAdminDropdown ? `
              <div class="relative group">
                <button class="hover:bg-blue-700 px-2 py-2 rounded min-h-[44px] flex items-center" title="Admin">
                  <i class="fas fa-cog"></i>
                  <span class="ml-1 hidden lg:inline">Admin</span>
                  <i class="fas fa-chevron-down ml-1 text-xs"></i>
                </button>
                <div class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div class="py-1">
                    ${adminDropdownItems.map(item => `
                      <a href="${item.href}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 ${currentPage === item.key ? 'bg-blue-50 text-blue-700' : ''}">
                        <i class="${item.icon} mr-2"></i>${item.label}
                      </a>
                    `).join('')}
                  </div>
                </div>
              </div>
            ` : ''}
            <a href="/profile"
               data-testid="profile-menu"
               class="hover:bg-blue-700 px-2 py-2 rounded min-h-[44px] flex items-center ${getNavClass('profile')}" title="Profiil">
              <i class="fas fa-user"></i>
              <span class="ml-1 hidden lg:inline">Profiil</span>
            </a>
            <a href="/auth/logout"
               data-testid="logout-btn"
               class="hover:bg-blue-700 px-2 py-2 rounded min-h-[44px] flex items-center" title="Logi välja">
              <i class="fas fa-sign-out-alt"></i>
              <span class="ml-1 hidden lg:inline">Välja</span>
            </a>
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="md:hidden">
          <div class="flex justify-between items-center py-3">
            <h1 class="text-lg font-bold">CMMS</h1>
            <button onclick="toggleMobileMenu()" class="p-2 rounded hover:bg-blue-700 min-h-[44px] min-w-[44px]">
              <i id="mobile-menu-icon" class="fas fa-bars text-xl"></i>
            </button>
          </div>

          <!-- Mobile Menu -->
          <div id="mobile-menu" class="hidden pb-4">
            <div class="space-y-2">
              ${navItems
                .map(
                  item => `
                <a href="${item.href}" class="${getMobileNavClass(item.key)}">
                  <i class="${item.icon} mr-2"></i>${item.label}
                </a>
              `
                )
                .join('')}
              ${showAdminDropdown ? `
                <div class="border-t border-blue-500 pt-2 mt-2">
                  <div class="text-xs text-blue-200 px-4 py-1 uppercase tracking-wide">Admin</div>
                  ${adminDropdownItems.map(item => `
                    <a href="${item.href}" class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center ${currentPage === item.key ? 'bg-blue-800' : ''}">
                      <i class="${item.icon} mr-2"></i>${item.label}
                    </a>
                  `).join('')}
                </div>
              ` : ''}
              <a href="/profile"
                 class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center ${currentPage === 'profile' ? 'bg-blue-800' : ''}">
                <i class="fas fa-user mr-2"></i>Profiil
              </a>
              <a href="/auth/logout"
                 class="block hover:bg-blue-700 px-4 py-3 rounded text-center min-h-[44px] flex items-center justify-center border-t border-blue-500 mt-2 pt-2">
                <i class="fas fa-sign-out-alt mr-2"></i>Logi välja
              </a>
            </div>
          </div>
        </div>
      </div>
    </nav>
  `;
}

// Mobile menu toggle script (with script tags for standalone use)
export const mobileMenuScript = `
<script>
  // Mobile menu toggle
  function toggleMobileMenu() {
    const menu = document.getElementById('mobile-menu');
    const icon = document.getElementById('mobile-menu-icon');

    if (menu.classList.contains('hidden')) {
      menu.classList.remove('hidden');
      icon.classList.remove('fa-bars');
      icon.classList.add('fa-times');
    } else {
      menu.classList.add('hidden');
      icon.classList.remove('fa-times');
      icon.classList.add('fa-bars');
    }
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    const menu = document.getElementById('mobile-menu');
    const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

    if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
      toggleMobileMenu();
    }
  });
</script>
`;

// Mobile menu toggle script (without script tags for use inside existing script blocks)
export const mobileMenuScriptInline = `
  // Mobile menu toggle
  function toggleMobileMenu() {
    const menu = document.getElementById('mobile-menu');
    const icon = document.getElementById('mobile-menu-icon');

    if (menu.classList.contains('hidden')) {
      menu.classList.remove('hidden');
      icon.classList.remove('fa-bars');
      icon.classList.add('fa-times');
    } else {
      menu.classList.add('hidden');
      icon.classList.remove('fa-times');
      icon.classList.add('fa-bars');
    }
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    const menu = document.getElementById('mobile-menu');
    const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

    if (!button && !menu.contains(event.target) && !menu.classList.contains('hidden')) {
      toggleMobileMenu();
    }
  });
`;
