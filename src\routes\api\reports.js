import { Hono } from 'hono';
import { Machine } from '../../models/Machine.js';
import { MachineGroup } from '../../models/MachineGroup.js';
import { Issue } from '../../models/Issue.js';
import { MaintenanceRequest } from '../../models/MaintenanceRequest.js';

const reportsRoutes = new Hono();

// Get comprehensive group analytics
reportsRoutes.get('/groups/analytics', async c => {
  try {
    const query = c.req.query();
    const startDate = query.start_date || null;
    const endDate = query.end_date || null;
    const groupId = query.group_id ? parseInt(query.group_id) : null;

    // Get all groups or specific group
    const groups = groupId
      ? [await MachineGroup.findById(groupId)]
      : await MachineGroup.findAll({ is_active: true });

    const analytics = [];

    for (const group of groups) {
      if (!group) continue;

      // Get machines in this group
      const machines = await Machine.findAll({ group_id: group.id });
      const machineIds = machines.map(m => m.id);

      // Get issues for this group's machines
      const issueFilters = { machine_ids: machineIds };
      if (startDate) issueFilters.start_date = startDate;
      if (endDate) issueFilters.end_date = endDate;

      const issues = await Issue.findAll(issueFilters);
      const issueStats = {
        total: issues.length,
        open: issues.filter(i => i.status === 'open').length,
        in_progress: issues.filter(i => i.status === 'in_progress').length,
        resolved: issues.filter(i => i.status === 'resolved').length,
        critical: issues.filter(i => i.priority === 'critical').length,
        high: issues.filter(i => i.priority === 'high').length,
      };

      // Get maintenance requests for this group's machines
      const maintenanceFilters = { machine_ids: machineIds };
      if (startDate) maintenanceFilters.start_date = startDate;
      if (endDate) maintenanceFilters.end_date = endDate;

      const maintenance = await MaintenanceRequest.findAll(maintenanceFilters);
      const maintenanceStats = {
        total: maintenance.length,
        pending: maintenance.filter(m => m.status === 'pending').length,
        scheduled: maintenance.filter(m => m.status === 'scheduled').length,
        in_progress: maintenance.filter(m => m.status === 'in_progress').length,
        completed: maintenance.filter(m => m.status === 'completed').length,
        urgent: maintenance.filter(m => m.priority === 'urgent').length,
      };

      // Calculate machine status distribution
      const machineStats = {
        total: machines.length,
        online: machines.filter(m => m.status === 'online').length,
        offline: machines.filter(m => m.status === 'offline').length,
        maintenance: machines.filter(m => m.status === 'maintenance').length,
      };

      // Calculate efficiency metrics
      const totalIssues = issueStats.total;
      const resolvedIssues = issueStats.resolved;
      const resolutionRate =
        totalIssues > 0 ? ((resolvedIssues / totalIssues) * 100).toFixed(1) : 0;

      const totalMaintenance = maintenanceStats.total;
      const completedMaintenance = maintenanceStats.completed;
      const maintenanceCompletionRate =
        totalMaintenance > 0 ? ((completedMaintenance / totalMaintenance) * 100).toFixed(1) : 0;

      const uptime =
        machineStats.total > 0 ? ((machineStats.online / machineStats.total) * 100).toFixed(1) : 0;

      analytics.push({
        group: {
          id: group.id,
          name: group.name,
          description: group.description,
          color: group.color,
          icon: group.icon,
        },
        machines: machineStats,
        issues: issueStats,
        maintenance: maintenanceStats,
        metrics: {
          uptime_percentage: parseFloat(uptime),
          issue_resolution_rate: parseFloat(resolutionRate),
          maintenance_completion_rate: parseFloat(maintenanceCompletionRate),
          issues_per_machine:
            machineStats.total > 0 ? (totalIssues / machineStats.total).toFixed(2) : 0,
          maintenance_per_machine:
            machineStats.total > 0 ? (totalMaintenance / machineStats.total).toFixed(2) : 0,
        },
        period: {
          start_date: startDate,
          end_date: endDate,
        },
      });
    }

    return c.json({
      success: true,
      data: analytics,
      summary: {
        total_groups: analytics.length,
        total_machines: analytics.reduce((sum, a) => sum + a.machines.total, 0),
        total_issues: analytics.reduce((sum, a) => sum + a.issues.total, 0),
        total_maintenance: analytics.reduce((sum, a) => sum + a.maintenance.total, 0),
        average_uptime:
          analytics.length > 0
            ? (
                analytics.reduce((sum, a) => sum + a.metrics.uptime_percentage, 0) /
                analytics.length
              ).toFixed(1)
            : 0,
      },
    });
  } catch (error) {
    console.error('Error generating group analytics:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Get machine performance report
reportsRoutes.get('/machines/performance', async c => {
  try {
    const query = c.req.query();
    const startDate = query.start_date || null;
    const endDate = query.end_date || null;
    const groupId = query.group_id ? parseInt(query.group_id) : null;

    // Get machines with optional group filter
    const machineFilters = {};
    if (groupId) machineFilters.group_id = groupId;

    const machines = await Machine.findAll(machineFilters);
    const performance = [];

    for (const machine of machines) {
      // Get issues for this machine
      const issueFilters = { machine_id: machine.id };
      if (startDate) issueFilters.start_date = startDate;
      if (endDate) issueFilters.end_date = endDate;

      const issues = await Issue.findAll(issueFilters);

      // Get maintenance for this machine
      const maintenanceFilters = { machine_id: machine.id };
      if (startDate) maintenanceFilters.start_date = startDate;
      if (endDate) maintenanceFilters.end_date = endDate;

      const maintenance = await MaintenanceRequest.findAll(maintenanceFilters);

      // Calculate performance metrics
      const totalIssues = issues.length;
      const criticalIssues = issues.filter(i => i.priority === 'critical').length;
      const resolvedIssues = issues.filter(i => i.status === 'resolved').length;

      const totalMaintenance = maintenance.length;
      const completedMaintenance = maintenance.filter(m => m.status === 'completed').length;
      const urgentMaintenance = maintenance.filter(m => m.priority === 'urgent').length;

      // Calculate scores (0-100)
      const reliabilityScore =
        totalIssues === 0 ? 100 : Math.max(0, 100 - criticalIssues * 20 - totalIssues * 5);
      const maintenanceScore =
        totalMaintenance === 0 ? 100 : (completedMaintenance / totalMaintenance) * 100;
      const overallScore = (reliabilityScore + maintenanceScore) / 2;

      performance.push({
        machine: {
          id: machine.id,
          machine_number: machine.machine_number,
          name: machine.name,
          status: machine.status,
          group_name: machine.group_name,
          group_color: machine.group_color,
        },
        issues: {
          total: totalIssues,
          critical: criticalIssues,
          resolved: resolvedIssues,
          resolution_rate: totalIssues > 0 ? ((resolvedIssues / totalIssues) * 100).toFixed(1) : 0,
        },
        maintenance: {
          total: totalMaintenance,
          completed: completedMaintenance,
          urgent: urgentMaintenance,
          completion_rate:
            totalMaintenance > 0 ? ((completedMaintenance / totalMaintenance) * 100).toFixed(1) : 0,
        },
        scores: {
          reliability: parseFloat(reliabilityScore.toFixed(1)),
          maintenance: parseFloat(maintenanceScore.toFixed(1)),
          overall: parseFloat(overallScore.toFixed(1)),
        },
      });
    }

    // Sort by overall score (best first)
    performance.sort((a, b) => b.scores.overall - a.scores.overall);

    return c.json({
      success: true,
      data: performance,
      summary: {
        total_machines: performance.length,
        average_reliability:
          performance.length > 0
            ? (
                performance.reduce((sum, p) => sum + p.scores.reliability, 0) / performance.length
              ).toFixed(1)
            : 0,
        average_maintenance_score:
          performance.length > 0
            ? (
                performance.reduce((sum, p) => sum + p.scores.maintenance, 0) / performance.length
              ).toFixed(1)
            : 0,
        average_overall_score:
          performance.length > 0
            ? (
                performance.reduce((sum, p) => sum + p.scores.overall, 0) / performance.length
              ).toFixed(1)
            : 0,
        top_performers: performance.slice(0, 5),
        bottom_performers: performance.slice(-5).reverse(),
      },
    });
  } catch (error) {
    console.error('Error generating machine performance report:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Get trend analysis
reportsRoutes.get('/trends', async c => {
  try {
    const query = c.req.query();
    const days = parseInt(query.days) || 30;
    const groupId = query.group_id ? parseInt(query.group_id) : null;

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get machines with optional group filter
    const machineFilters = {};
    if (groupId) machineFilters.group_id = groupId;
    const machines = await Machine.findAll(machineFilters);
    const machineIds = machines.map(m => m.id);

    // Generate daily data points
    const trends = [];
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      // Get issues for this date
      const dayIssues = await Issue.findAll({
        machine_ids: machineIds,
        start_date: dateStr,
        end_date: dateStr,
      });

      // Get maintenance for this date
      const dayMaintenance = await MaintenanceRequest.findAll({
        machine_ids: machineIds,
        start_date: dateStr,
        end_date: dateStr,
      });

      trends.push({
        date: dateStr,
        issues: {
          total: dayIssues.length,
          critical: dayIssues.filter(i => i.priority === 'critical').length,
          resolved: dayIssues.filter(i => i.status === 'resolved').length,
        },
        maintenance: {
          total: dayMaintenance.length,
          urgent: dayMaintenance.filter(m => m.priority === 'urgent').length,
          completed: dayMaintenance.filter(m => m.status === 'completed').length,
        },
      });
    }

    return c.json({
      success: true,
      data: trends,
      period: {
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0],
        days: days,
      },
      summary: {
        total_issues: trends.reduce((sum, t) => sum + t.issues.total, 0),
        total_maintenance: trends.reduce((sum, t) => sum + t.maintenance.total, 0),
        peak_issues_day: trends.reduce(
          (max, t) => (t.issues.total > max.issues.total ? t : max),
          trends[0]
        ),
        peak_maintenance_day: trends.reduce(
          (max, t) => (t.maintenance.total > max.maintenance.total ? t : max),
          trends[0]
        ),
      },
    });
  } catch (error) {
    console.error('Error generating trend analysis:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

export { reportsRoutes };
