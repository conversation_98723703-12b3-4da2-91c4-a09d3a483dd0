import { describe, it, expect, beforeEach, afterEach, afterAll } from 'bun:test';
import { 
  generateMachineSecret, 
  validateQRAccess, 
  logQRAccess,
  checkRateLimit,
  isInternalNetwork 
} from '../../../src/services/qr-security.js';
import { pool } from '../../../src/config/database.js';

describe('QR Security Service', () => {
  beforeEach(async () => {
    // Clean up test data
    await pool.execute('DELETE FROM qr_access_logs WHERE ip_address LIKE "test_%"');
    await pool.execute('DELETE FROM qr_rate_limits WHERE ip_address LIKE "test_%"');
  });

  afterEach(async () => {
    // Clean up test data
    await pool.execute('DELETE FROM qr_access_logs WHERE ip_address LIKE "test_%"');
    await pool.execute('DELETE FROM qr_rate_limits WHERE ip_address LIKE "test_%"');
  });

  // Close pool after all tests
  afterAll(async () => {
    await pool.end();
  });

  describe('generateMachineSecret', () => {
    it('should generate a 32-character secret', () => {
      const secret = generateMachineSecret();
      expect(secret).toHaveLength(32);
      expect(typeof secret).toBe('string');
    });

    it('should generate unique secrets', () => {
      const secret1 = generateMachineSecret();
      const secret2 = generateMachineSecret();
      expect(secret1).not.toBe(secret2);
    });

    it('should only contain alphanumeric characters', () => {
      const secret = generateMachineSecret();
      expect(secret).toMatch(/^[a-zA-Z0-9]+$/);
    });
  });

  describe('validateQRAccess', () => {
    it('should validate correct machine and secret', async () => {
      // Create test machine with secret
      const [result] = await pool.execute(
        'INSERT INTO machines (machine_number, name, qr_secret) VALUES (?, ?, ?)',
        ['TEST-001', 'Test Machine', 'test_secret_123456789012345678']
      );
      const machineId = result.insertId;

      const validation = await validateQRAccess('TEST-001', 'test_secret_123456789012345678');
      
      expect(validation.valid).toBe(true);
      expect(validation.machine.id).toBe(machineId);
      expect(validation.machine.machine_number).toBe('TEST-001');

      // Cleanup
      await pool.execute('DELETE FROM machines WHERE id = ?', [machineId]);
    });

    it('should reject invalid secret', async () => {
      // Create test machine with secret
      const [result] = await pool.execute(
        'INSERT INTO machines (machine_number, name, qr_secret) VALUES (?, ?, ?)',
        ['TEST-002', 'Test Machine 2', 'correct_secret_123456789012345']
      );
      const machineId = result.insertId;

      const validation = await validateQRAccess('TEST-002', 'wrong_secret_123456789012345678');
      
      expect(validation.valid).toBe(false);
      expect(validation.error).toBe('invalid_secret');

      // Cleanup
      await pool.execute('DELETE FROM machines WHERE id = ?', [machineId]);
    });

    it('should reject non-existent machine', async () => {
      const validation = await validateQRAccess('NONEXISTENT', 'any_secret_123456789012345678');
      
      expect(validation.valid).toBe(false);
      expect(validation.error).toBe('machine_not_found');
    });

    it('should reject machine without secret', async () => {
      // Create test machine without secret
      const [result] = await pool.execute(
        'INSERT INTO machines (machine_number, name) VALUES (?, ?)',
        ['TEST-003', 'Test Machine 3']
      );
      const machineId = result.insertId;

      const validation = await validateQRAccess('TEST-003', 'any_secret_123456789012345678');
      
      expect(validation.valid).toBe(false);
      expect(validation.error).toBe('no_secret');

      // Cleanup
      await pool.execute('DELETE FROM machines WHERE id = ?', [machineId]);
    });
  });

  describe('logQRAccess', () => {
    it('should log successful access', async () => {
      await logQRAccess({
        machineId: 1,
        machineNumber: 'TEST-LOG-001',
        secretAttempt: 'correct_secret',
        ipAddress: 'test_*************',
        userAgent: 'Test Browser',
        accessType: 'valid',
        success: true
      });

      const [logs] = await pool.execute(
        'SELECT * FROM qr_access_logs WHERE ip_address = ?',
        ['test_*************']
      );

      expect(logs).toHaveLength(1);
      expect(logs[0].machine_number).toBe('TEST-LOG-001');
      expect(logs[0].access_type).toBe('valid');
      expect(logs[0].success).toBe(1);
    });

    it('should log failed access attempts', async () => {
      await logQRAccess({
        machineId: null,
        machineNumber: 'TEST-LOG-002',
        secretAttempt: 'wrong_secret',
        ipAddress: 'test_192.168.1.101',
        userAgent: 'Test Browser',
        accessType: 'invalid_secret',
        success: false
      });

      const [logs] = await pool.execute(
        'SELECT * FROM qr_access_logs WHERE ip_address = ?',
        ['test_192.168.1.101']
      );

      expect(logs).toHaveLength(1);
      expect(logs[0].access_type).toBe('invalid_secret');
      expect(logs[0].success).toBe(0);
    });
  });

  describe('checkRateLimit', () => {
    it('should allow access within rate limit', async () => {
      const result = await checkRateLimit('test_192.168.1.200');
      expect(result.allowed).toBe(true);
      expect(result.attempts).toBe(1);
    });

    it('should block access after exceeding rate limit', async () => {
      const ip = 'test_192.168.1.201';
      
      // Make 10 attempts (should be allowed)
      for (let i = 0; i < 10; i++) {
        const result = await checkRateLimit(ip);
        expect(result.allowed).toBe(true);
      }

      // 11th attempt should be blocked
      const result = await checkRateLimit(ip);
      expect(result.allowed).toBe(false);
      expect(result.attempts).toBe(11);
    });

    it('should reset rate limit after time window', async () => {
      const ip = 'test_192.168.1.202';
      
      // Simulate old window by manually setting window_start
      await pool.execute(
        'INSERT INTO qr_rate_limits (ip_address, attempts, window_start) VALUES (?, ?, ?)',
        [ip, 10, new Date(Date.now() - 16 * 60 * 1000)] // 16 minutes ago
      );

      const result = await checkRateLimit(ip);
      expect(result.allowed).toBe(true);
      expect(result.attempts).toBe(1); // Should reset to 1
    });
  });

  describe('isInternalNetwork', () => {
    it('should allow localhost addresses', () => {
      expect(isInternalNetwork('127.0.0.1')).toBe(true);
      expect(isInternalNetwork('::1')).toBe(true);
      expect(isInternalNetwork('localhost')).toBe(true);
    });

    it('should allow private network ranges', () => {
      expect(isInternalNetwork('*************')).toBe(true);
      expect(isInternalNetwork('*********')).toBe(true);
      expect(isInternalNetwork('***********')).toBe(true);
    });

    it('should block external addresses', () => {
      expect(isInternalNetwork('*******')).toBe(false);
      expect(isInternalNetwork('*******')).toBe(false);
      expect(isInternalNetwork('**************')).toBe(false);
    });

    it('should handle development environment', () => {
      // In development, we might allow all IPs
      process.env.NODE_ENV = 'development';
      expect(isInternalNetwork('*******')).toBe(true);
      
      // Reset
      process.env.NODE_ENV = 'test';
    });
  });
});
