-- Create issue_materials table for tracking materials used in issue resolution
CREATE TABLE IF NOT EXISTS issue_materials (
  id INT AUTO_INCREMENT PRIMARY KEY,
  issue_id INT NOT NULL,
  material_type ENUM('stock_part', 'external_material') NOT NULL DEFAULT 'stock_part',
  
  -- For stock parts from inventory
  part_id INT NULL,
  
  -- For external materials not tracked in inventory
  material_name VARCHAR(255) NULL,
  material_description TEXT NULL,
  supplier VARCHAR(255) NULL,
  
  -- Common fields
  quantity DECIMAL(10,3) NOT NULL DEFAULT 1.000,
  unit_of_measure VARCHAR(50) NOT NULL DEFAULT 'tk',
  unit_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  total_cost DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,
  
  -- Status tracking
  status ENUM('planned', 'ordered', 'received', 'used') NOT NULL DEFAULT 'planned',
  
  -- Additional information
  notes TEXT NULL,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Foreign key constraints
  FOREIGN KEY (issue_id) REFERENCES issues(id) ON DELETE CASCADE,
  FOREIGN KEY (part_id) REFERENCES parts(id) ON DELETE SET NULL,
  
  -- Indexes for performance
  INDEX idx_issue_id (issue_id),
  INDEX idx_part_id (part_id),
  INDEX idx_material_type (material_type),
  INDEX idx_status (status)

  -- Note: CHECK constraints with NULL checks not supported in this MariaDB version
  -- Data validation will be handled at application level
);

-- Create view for issue materials summary
CREATE OR REPLACE VIEW issue_materials_summary AS
SELECT 
  issue_id,
  COUNT(*) as total_materials,
  SUM(CASE WHEN material_type = 'stock_part' THEN 1 ELSE 0 END) as stock_parts_count,
  SUM(CASE WHEN material_type = 'external_material' THEN 1 ELSE 0 END) as external_materials_count,
  SUM(total_cost) as total_materials_cost,
  SUM(CASE WHEN material_type = 'stock_part' THEN total_cost ELSE 0 END) as stock_parts_cost,
  SUM(CASE WHEN material_type = 'external_material' THEN total_cost ELSE 0 END) as external_materials_cost
FROM issue_materials
GROUP BY issue_id;
