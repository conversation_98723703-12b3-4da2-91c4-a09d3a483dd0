import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import {
  calculateMTBF,
  calculateMTTR,
  calculateReliabilityScore,
  predictNextFailure,
  calculateCostAnalysis,
  getMaintenanceRecommendation,
  generateAdvancedAnalytics
} from '../../../src/services/analytics.js';

describe('Analytics Service', () => {
  beforeEach(async () => {
    // Setup test data - will be implemented when analytics service is created
  });

  afterEach(async () => {
    // Clean up test data
  });

  describe('calculateMTBF', () => {
    it('should calculate Mean Time Between Failures correctly', () => {
      const issues = [
        { created_at: new Date('2024-01-01') },
        { created_at: new Date('2024-01-11') },
        { created_at: new Date('2024-01-21') }
      ];

      const mtbf = calculateMTBF(issues);
      expect(mtbf).toBe(10); // 10 days between failures
    });

    it('should return null for machines with no failure history', () => {
      const mtbf = calculateMTBF([]);
      expect(mtbf).toBeNull();
    });

    it('should return null for machines with only one failure', () => {
      const issues = [{ created_at: new Date('2024-01-01') }];
      const mtbf = calculateMTBF(issues);
      expect(mtbf).toBeNull();
    });
  });

  describe('calculateMTTR', () => {
    it('should calculate Mean Time To Repair correctly', () => {
      const issues = [
        {
          created_at: new Date('2024-01-01T10:00:00'),
          resolved_at: new Date('2024-01-01T12:00:00'),
          status: 'resolved'
        },
        {
          created_at: new Date('2024-01-02T10:00:00'),
          resolved_at: new Date('2024-01-02T14:00:00'),
          status: 'resolved'
        }
      ];

      const mttr = calculateMTTR(issues);
      expect(mttr).toBe(3); // Average of 2 and 4 hours
    });

    it('should return null for machines with no resolved issues', () => {
      const mttr = calculateMTTR([]);
      expect(mttr).toBeNull();
    });

    it('should ignore unresolved issues', () => {
      const issues = [
        {
          created_at: new Date('2024-01-01T10:00:00'),
          resolved_at: new Date('2024-01-01T12:00:00'),
          status: 'resolved'
        },
        {
          created_at: new Date('2024-01-02T10:00:00'),
          status: 'open'
        }
      ];

      const mttr = calculateMTTR(issues);
      expect(mttr).toBe(2); // Only the resolved issue counts
    });
  });

  describe('calculateReliabilityScore', () => {
    it('should calculate reliability score based on MTBF, MTTR, and issue frequency', () => {
      const machineData = {
        mtbf: 30, // 30 days between failures
        mttr: 1,  // 1 hour to repair
        issueCount: 2,
        totalDays: 60
      };

      const reliabilityScore = calculateReliabilityScore(machineData);

      expect(reliabilityScore).toBeGreaterThan(0);
      expect(reliabilityScore).toBeLessThanOrEqual(100);
      expect(reliabilityScore).toBeGreaterThan(70); // Should be high with good MTBF and low MTTR
    });

    it('should return lower score for machines with frequent failures', () => {
      const machineData = {
        mtbf: 2,  // 2 days between failures
        mttr: 4,  // 4 hours to repair
        issueCount: 15,
        totalDays: 30
      };

      const reliabilityScore = calculateReliabilityScore(machineData);
      expect(reliabilityScore).toBeLessThan(70); // Should be low with frequent failures
    });

    it('should return default score for machines with no history', () => {
      const machineData = {
        mtbf: null,
        mttr: null,
        issueCount: 0,
        totalDays: 0
      };

      const reliabilityScore = calculateReliabilityScore(machineData);
      expect(reliabilityScore).toBe(75); // Default score
    });
  });

  describe('predictNextFailure', () => {
    it('should predict next failure based on historical data', () => {
      const issues = [
        { created_at: new Date('2024-01-01') },
        { created_at: new Date('2024-01-16') },
        { created_at: new Date('2024-01-31') }
      ];

      const prediction = predictNextFailure(issues);

      expect(prediction).toHaveProperty('predictedDate');
      expect(prediction).toHaveProperty('confidence');
      expect(prediction).toHaveProperty('daysFromNow');
      expect(prediction.confidence).toBeGreaterThan(0);
      expect(prediction.confidence).toBeLessThanOrEqual(100);
    });

    it('should return null for machines with insufficient data', () => {
      const prediction = predictNextFailure([]);
      expect(prediction).toBeNull();
    });
  });

  describe('getMaintenanceRecommendation', () => {
    it('should recommend immediate action for low reliability', () => {
      const machineData = {
        reliabilityScore: 40,
        nextFailurePrediction: { daysFromNow: 15 }
      };

      const recommendation = getMaintenanceRecommendation(machineData);

      expect(recommendation.priority).toBe('high');
      expect(recommendation.action).toContain('Immediate');
    });

    it('should recommend preventive maintenance for upcoming failures', () => {
      const machineData = {
        reliabilityScore: 75,
        nextFailurePrediction: { daysFromNow: 5 }
      };

      const recommendation = getMaintenanceRecommendation(machineData);

      expect(recommendation.priority).toBe('medium');
      expect(recommendation.action).toContain('preventive');
    });

    it('should recommend monitoring for stable machines', () => {
      const machineData = {
        reliabilityScore: 85,
        nextFailurePrediction: { daysFromNow: 45 }
      };

      const recommendation = getMaintenanceRecommendation(machineData);

      expect(recommendation.priority).toBe('low');
      expect(recommendation.action).toContain('monitor');
    });
  });
});
