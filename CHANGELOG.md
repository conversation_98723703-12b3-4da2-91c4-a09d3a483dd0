# Changelog

All notable changes to the CMMS project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Issue reporting system (US-004)
- Operator mobile interface (US-005)
- Maintenance planning (US-006)
- Spare parts management (US-007)
- Document management (US-008)
- Statistics and reporting (US-009)

## [0.1.0] - 2025-05-27

### Added
- Initial project setup with Bun, Hono, and MariaDB
- Machine management system (US-001)
  - Create, read, update, delete machines
  - Automatic QR code generation for each machine
  - Machine details view with QR code display
- QR code functionality (US-002)
  - Generate QR codes linking to operator interface
  - Download QR codes as PNG files
  - Store QR codes in database as LONGBLOB
- Machine editing capabilities (US-003)
  - Update machine information
  - Form validation and error handling
- Database schema with comprehensive tables
  - machines, operators, issues, maintenance_records
  - spare_parts, machine_parts, documents, development_projects
- Test-Driven Development (TDD) implementation
  - API unit tests with Vitest
  - E2E tests with Playwright
  - Test setup and fixtures
- Development environment setup
  - Environment configuration (.env.example)
  - Database migrations
  - Setup scripts for Linux/macOS and Windows
- Documentation
  - Comprehensive README.md
  - API documentation
  - Project structure documentation
- Admin web interface
  - Dashboard with machine overview
  - Machine list and detail views
  - Add new machine form
  - Server-side rendering with EJS templates
  - Tailwind CSS styling

### Technical Details
- **Backend**: Hono.js framework with Bun runtime
- **Database**: MariaDB/MySQL with connection pooling
- **Frontend**: Server-side rendering (EJS) + Tailwind CSS
- **Testing**: Playwright (E2E) + Vitest (API/Unit)
- **QR Codes**: qrcode library with PNG generation
- **File Storage**: Database LONGBLOB for QR codes and future documents

### API Endpoints
- `GET /api/machines` - List all machines
- `POST /api/machines` - Create new machine
- `GET /api/machines/:id` - Get machine details
- `PUT /api/machines/:id` - Update machine
- `DELETE /api/machines/:id` - Delete machine
- `GET /api/files/qr/:machineId` - Download QR code

### Web Routes
- `/` - Redirect to admin dashboard
- `/admin` - Admin dashboard
- `/machines` - Machine list
- `/machines/new` - Add new machine form
- `/machines/:id` - Machine details view

### Development
- TDD workflow implemented (Red-Green-Refactor)
- Comprehensive test coverage for core functionality
- Database migrations and schema management
- Environment-based configuration
- Cross-platform setup scripts

## [0.0.1] - 2025-05-27

### Added
- Initial project structure
- Basic configuration files
- Development environment setup
