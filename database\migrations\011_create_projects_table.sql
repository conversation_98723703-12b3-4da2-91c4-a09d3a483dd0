-- Migration 011: Create projects table
-- Date: 2024-01-11

CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    machine_id INT NULL,
    status ENUM('planning', 'active', 'on_hold', 'completed', 'cancelled') DEFAULT 'planning',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    start_date DATE,
    end_date DATE,
    estimated_budget DECIMAL(12,2),
    actual_cost DECIMAL(12,2),
    project_manager VARCHAR(255),
    assigned_team TEXT,
    progress_percentage INT DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE SET NULL,
    INDEX idx_machine_id (machine_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date)
);

-- Add project_id to documents table for project documents
ALTER TABLE documents 
ADD COLUMN project_id INT NULL AFTER machine_id,
ADD FOREIGN KEY fk_document_project (project_id) REFERENCES projects(id) ON DELETE CASCADE;

-- Create index for project_id in documents
CREATE INDEX idx_document_project_id ON documents(project_id);

-- Insert sample projects
INSERT INTO projects (title, description, machine_id, status, priority, start_date, end_date, project_manager, estimated_budget) VALUES
('CNC Freespingi uuendamine', 'CNC freespingi tarkvara ja riistvara uuendamine', 1, 'active', 'high', '2024-01-15', '2024-02-15', 'Jaan Tamm', 15000.00),
('Keevitusjaama laiendamine', 'Uue keevitusposte lisamine tootmisliini', 2, 'planning', 'medium', '2024-02-01', '2024-03-01', 'Mari Kask', 25000.00),
('Hüdraulikasüsteemi remont', 'Hüdraulilise pressi süsteemi täielik remont', 3, 'completed', 'high', '2023-12-01', '2024-01-10', 'Peeter Saar', 8000.00),
('Lõikepingi hooldus', 'Lõikepingi preventivne hooldus ja kalibreerimine', 4, 'on_hold', 'low', '2024-01-20', '2024-01-25', 'Anna Mets', 3000.00),
('Kompressori vahetus', 'Vana kompressori asendamine uue energiatõhusaga', 5, 'planning', 'medium', '2024-03-01', '2024-03-15', 'Toomas Kivi', 12000.00);
