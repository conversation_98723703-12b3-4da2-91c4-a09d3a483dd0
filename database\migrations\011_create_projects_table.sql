-- Migration 011: Create projects table
-- Date: 2024-01-11

-- Migration 011: Create development_projects table (updated to match schema.sql)
-- Date: 2024-01-11

CREATE TABLE IF NOT EXISTS development_projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    initiator <PERSON><PERSON><PERSON><PERSON>(255),
    start_date DATE,
    end_date DATE,
    status ENUM('active', 'completed', 'cancelled', 'on_hold') DEFAULT 'active',
    progress_percentage INT DEFAULT 0,
    tags JSO<PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- project_machines (projekti masinad)
CREATE TABLE IF NOT EXISTS project_machines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    machine_id INT NOT NULL,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (project_id) REFERENCES development_projects(id) ON DELETE CASCADE,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_machine (project_id, machine_id)
);

-- Create indexes for performance
CREATE INDEX idx_development_projects_status ON development_projects(status);
CREATE INDEX idx_development_projects_start_date ON development_projects(start_date);
CREATE INDEX idx_development_projects_end_date ON development_projects(end_date);
CREATE INDEX idx_project_machines_project ON project_machines(project_id);
CREATE INDEX idx_project_machines_machine ON project_machines(machine_id);

-- Insert sample development projects
INSERT INTO development_projects (title, description, initiator, start_date, end_date, status, progress_percentage, tags) VALUES
('CNC Freespingi moderniseerimine', 'CNC freespingi tarkvara ja riistvara uuendamine tänapäevaste standarditega', 'Jaan Tamm', '2024-01-15', '2024-03-15', 'active', 65, JSON_ARRAY('automation', 'software', 'hardware')),
('Keevitusjaama laiendus', 'Uue keevitusposte lisamine tootmisliini suurendamiseks', 'Mari Kask', '2024-02-01', '2024-04-01', 'active', 25, JSON_ARRAY('expansion', 'welding', 'capacity')),
('Hüdraulikasüsteemi täiustamine', 'Hüdraulilise pressi efektiivsuse parandamine ja energiatarbimise vähendamine', 'Peeter Saar', '2023-11-01', '2024-01-31', 'completed', 100, JSON_ARRAY('hydraulics', 'efficiency', 'energy')),
('Lõikeseadmete automaatika', 'Lõikepinkide automatiseerimise projekt', 'Anna Mets', '2024-03-01', '2024-06-01', 'on_hold', 15, JSON_ARRAY('automation', 'cutting', 'efficiency')),
('Kompressorjaama uuendus', 'Kompressorjaama uuendamine energiatõhusate seadmetega', 'Toomas Kivi', '2024-04-01', '2024-07-01', 'active', 5, JSON_ARRAY('energy', 'compressor', 'infrastructure'))
ON DUPLICATE KEY UPDATE title = title;

-- Link some projects to machines
INSERT INTO project_machines (project_id, machine_id) VALUES
(1, 1), -- CNC modernization project linked to CNC machine
(2, 2), -- Welding expansion project linked to welding station
(3, 3), -- Hydraulic improvement project linked to hydraulic press
(4, 4), -- Cutting automation project linked to cutting machine
(5, 5)  -- Compressor renovation project linked to compressor
ON DUPLICATE KEY UPDATE project_id = project_id;
