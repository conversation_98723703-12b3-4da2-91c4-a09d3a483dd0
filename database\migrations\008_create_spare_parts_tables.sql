-- Spare parts management tables (updated to match schema.sql)
-- Migration 008: Spare Parts Management System

-- 1. Spare parts main table
CREATE TABLE IF NOT EXISTS spare_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    part_number VARCHAR(100) UNIQUE NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    manufacturer VA<PERSON><PERSON><PERSON>(255),
    category VARCHAR(100),
    location VARCHAR(255),
    quantity_in_stock INT DEFAULT 0,
    minimum_stock INT DEFAULT 0,
    unit_price DECIMAL(10,2),
    supplier VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Machine-parts relationships
CREATE TABLE IF NOT EXISTS machine_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    machine_id INT NOT NULL,
    part_id INT NOT NULL,
    quantity_needed INT DEFAULT 1,
    FOREIGN KEY (machine_id) REFERENCES machines(id) ON DELETE CASCADE,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (part_id) REFERENCES spare_parts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_machine_part (machine_id, part_id)
);

-- Create indexes for performance
CREATE INDEX idx_spare_parts_number ON spare_parts(part_number);
CREATE INDEX idx_spare_parts_category ON spare_parts(category);
CREATE INDEX idx_spare_parts_location ON spare_parts(location);
CREATE INDEX idx_spare_parts_low_stock ON spare_parts(quantity_in_stock, minimum_stock);
CREATE INDEX idx_machine_parts_machine ON machine_parts(machine_id);
CREATE INDEX idx_machine_parts_part ON machine_parts(part_id);

-- Insert sample spare parts
INSERT INTO spare_parts (part_number, name, description, manufacturer, category, location, quantity_in_stock, minimum_stock, unit_price, supplier) VALUES
('PART-001', 'Mootori rihm', 'Transmissiooni rihm CNC freespingi jaoks', 'Gates', 'mechanical', 'Riiulid A1-A3', 5, 2, 45.50, 'Industrial Supply OÜ'),
('PART-002', 'Hüdrauliline tihend', 'O-ring 25x3mm NBR', 'Parker', 'hydraulic', 'Kast B2', 20, 5, 3.25, 'Hüdraulika Expert AS'),
('PART-003', 'Elektrimootorit kondensaator', '10µF 450V kondensaator', 'Schneider', 'electrical', 'Elektrikapp E1', 3, 1, 28.90, 'Elektritehnika OÜ'),
('PART-004', 'Laagri komplekt', 'Radiaalkuullaager 6205-2RS', 'SKF', 'mechanical', 'Riiulid A5-A7', 8, 3, 15.75, 'Bearing World AS'),
('PART-005', 'Filtrid', 'Õhufilter kompressorile', 'Mann Filter', 'mechanical', 'Riiulid C1', 12, 4, 22.40, 'Filter Solutions OÜ')
ON DUPLICATE KEY UPDATE part_number = part_number;

-- Link some parts to machines
INSERT INTO machine_parts (machine_id, part_id, quantity_needed) VALUES
(1, 1, 2), -- CNC freespink vajab 2 mootori rihma
(1, 4, 4), -- CNC freespink vajab 4 laagrit
(2, 3, 1), -- Keevituspost vajab 1 kondensaatorit
(3, 2, 6), -- Hüdrauliline press vajab 6 tihendi
(3, 4, 2), -- Hüdrauliline press vajab 2 laagrit
(5, 5, 2)  -- Kompressor vajab 2 filtrit
ON DUPLICATE KEY UPDATE quantity_needed = quantity_needed;
