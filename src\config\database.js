import mysql from 'mysql2/promise';

const config = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'cmms_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
};

export const pool = mysql.createPool(config);

// Debug function to check table structure
export async function checkTableStructure(tableName) {
  try {
    const connection = await pool.getConnection();
    const [rows] = await connection.execute(`DESCRIBE ${tableName}`);
    console.log(`\n📋 Table structure for ${tableName}:`);
    console.table(rows);
    connection.release();
    return rows;
  } catch (error) {
    console.error(`❌ Error checking table ${tableName}:`, error.message);
    return null;
  }
}

// Function to check if table exists
export async function tableExists(tableName) {
  try {
    const connection = await pool.getConnection();
    const [rows] = await connection.execute(
      `SELECT COUNT(*) as count FROM information_schema.tables 
       WHERE table_schema = ? AND table_name = ?`,
      [config.database, tableName]
    );
    connection.release();
    return rows[0].count > 0;
  } catch (error) {
    console.error(`❌ Error checking if table ${tableName} exists:`, error.message);
    return false;
  }
}

export async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

export default pool;
