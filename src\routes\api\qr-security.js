import { Hono } from 'hono';
import { getQRAccessLogs } from '../../services/qr-security.js';
import { requireAuth, requireRole } from '../../middleware/auth.js';

export const qrSecurityApiRoutes = new Hono();

// GET /api/qr-security/logs - Get QR access logs (admin only)
qrSecurityApiRoutes.get('/logs', requireAuth, requireRole('admin'), async c => {
  try {
    const query = c.req.query();
    
    const options = {
      page: parseInt(query.page) || 1,
      limit: parseInt(query.limit) || 50,
      machineId: query.machine_id ? parseInt(query.machine_id) : null,
      accessType: query.access_type || null,
      startDate: query.start_date || null,
      endDate: query.end_date || null
    };

    const result = await getQRAccessLogs(options);

    return c.json({
      success: true,
      logs: result.logs,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('Error fetching QR access logs:', error);
    return c.json({ error: 'Failed to fetch QR access logs' }, 500);
  }
});

// GET /api/qr-security/stats - Get QR security statistics (admin only)
qrSecurityApiRoutes.get('/stats', requireAuth, requireRole('admin'), async c => {
  try {
    // Get basic statistics from the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const result = await getQRAccessLogs({
      startDate: thirtyDaysAgo.toISOString().split('T')[0],
      limit: 1000 // Get more records for stats
    });

    const logs = result.logs;

    // Calculate statistics
    const stats = {
      total_accesses: logs.length,
      successful_accesses: logs.filter(log => log.success).length,
      failed_accesses: logs.filter(log => !log.success).length,
      unique_ips: [...new Set(logs.map(log => log.ip_address))].length,
      access_types: {},
      daily_stats: {},
      top_machines: {},
      suspicious_ips: []
    };

    // Count access types
    logs.forEach(log => {
      stats.access_types[log.access_type] = (stats.access_types[log.access_type] || 0) + 1;
    });

    // Daily statistics
    logs.forEach(log => {
      const date = log.created_at.split(' ')[0]; // Get date part
      if (!stats.daily_stats[date]) {
        stats.daily_stats[date] = { total: 0, successful: 0, failed: 0 };
      }
      stats.daily_stats[date].total++;
      if (log.success) {
        stats.daily_stats[date].successful++;
      } else {
        stats.daily_stats[date].failed++;
      }
    });

    // Top machines by access count
    logs.forEach(log => {
      if (log.machine_number) {
        stats.top_machines[log.machine_number] = (stats.top_machines[log.machine_number] || 0) + 1;
      }
    });

    // Find suspicious IPs (multiple failed attempts)
    const ipFailures = {};
    logs.filter(log => !log.success).forEach(log => {
      ipFailures[log.ip_address] = (ipFailures[log.ip_address] || 0) + 1;
    });

    stats.suspicious_ips = Object.entries(ipFailures)
      .filter(([ip, count]) => count >= 3)
      .map(([ip, count]) => ({ ip_address: ip, failed_attempts: count }))
      .sort((a, b) => b.failed_attempts - a.failed_attempts);

    return c.json({
      success: true,
      stats: stats,
      period: '30 days'
    });
  } catch (error) {
    console.error('Error fetching QR security stats:', error);
    return c.json({ error: 'Failed to fetch QR security statistics' }, 500);
  }
});
